import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { api } from "./_generated/api";

export const addTwitterAccount = mutation({
  args: {
    handle: v.string(),
    displayName: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
    isMonitoringEnabled: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }
    
    // Get user first
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();
    
    if (!user) {
      throw new Error("User not found");
    }
    
    // Check if account already exists for this user
    const existingAccount = await ctx.db
      .query("twitterAccounts")
      .withIndex("by_handle", (q) => q.eq("handle", args.handle))
      .first();
    
    if (existingAccount && existingAccount.userId === user._id) {
      throw new Error("Twitter account already added");
    }
    
    // Create new Twitter account
    const accountId = await ctx.db.insert("twitterAccounts", {
      userId: user._id,
      handle: args.handle,
      displayName: args.displayName || args.handle,
      isActive: args.isActive !== false,
      isMonitoringEnabled: args.isMonitoringEnabled !== false,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
    
    return await ctx.db.get(accountId);
  },
});

export const removeTwitterAccount = mutation({
  args: {
    accountId: v.id("twitterAccounts"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }
    
    // Get the account to verify ownership
    const account = await ctx.db.get(args.accountId);
    if (!account) {
      throw new Error("Account not found");
    }
    
    // Get user to verify ownership
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();
    
    if (!user || account.userId !== user._id) {
      throw new Error("Not authorized to delete this account");
    }
    
    // Delete the account
    await ctx.db.delete(args.accountId);
    return { success: true };
  },
});

/**
 * Get active Twitter accounts for workflows
 */
export const getActiveAccounts = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }
    
    // Get user first
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();
    
    if (!user) {
      throw new Error("User not found");
    }
    
    // Get user's active accounts
    const accounts = await ctx.db
      .query("twitterAccounts")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("isActive"), true))
      .take(args.limit || 50);
    
    return accounts;
  },
});

/**
 * Update last scraped timestamp for an account
 */
export const updateLastScraped = mutation({
  args: {
    accountId: v.id("twitterAccounts"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }
    
    // Get the account to verify ownership
    const account = await ctx.db.get(args.accountId);
    if (!account) {
      throw new Error("Account not found");
    }
    
    // Get user to verify ownership
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();
    
    if (!user || account.userId !== user._id) {
      throw new Error("Not authorized to update this account");
    }
    
    // Update the last scraped timestamp
    await ctx.db.patch(args.accountId, {
      lastScrapedAt: Date.now(),
      updatedAt: Date.now(),
    });
    
    return { success: true };
  },
});

/**
 * Add Twitter account with bulk mention import
 */
export const addAccountWithBulkImport = mutation({
  args: {
    handle: v.string(),
    displayName: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
    isMonitoringEnabled: v.optional(v.boolean()),
    bulkImportConfig: v.object({
      maxMentions: v.number(),
      timeframeDays: v.number(),
      enableViralDetection: v.optional(v.boolean()),
      priorityMode: v.optional(v.boolean()),
    }),
  },
  returns: v.object({
    account: v.object({
      _id: v.id("twitterAccounts"),
      handle: v.string(),
      displayName: v.string(),
      isActive: v.boolean(),
      isMonitoringEnabled: v.boolean(),
      userId: v.id("users"),
      createdAt: v.number(),
      updatedAt: v.number(),
    }),
    mentionsImported: v.number(),
    importStatus: v.string(),
  }),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }
    
    // Get user first
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();
    
    if (!user) {
      throw new Error("User not found");
    }
    
    // Check if account already exists for this user
    const existingAccount = await ctx.db
      .query("twitterAccounts")
      .withIndex("by_handle", (q) => q.eq("handle", args.handle))
      .first();
    
    if (existingAccount && existingAccount.userId === user._id) {
      throw new Error("Twitter account already added");
    }
    
    // Create new Twitter account
    const accountId = await ctx.db.insert("twitterAccounts", {
      userId: user._id,
      handle: args.handle,
      displayName: args.displayName || args.handle,
      isActive: args.isActive !== false,
      isMonitoringEnabled: args.isMonitoringEnabled !== false,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
    
    const account = await ctx.db.get(accountId);
    if (!account) {
      throw new Error("Failed to create account");
    }
    
    let mentionsImported = 0;
    let importStatus = "no_import";
    
    // Perform bulk import if enabled
    if (args.isMonitoringEnabled !== false) {
      try {
        console.log(`🔄 Starting bulk import for @${args.handle} - ${args.bulkImportConfig.maxMentions} mentions from last ${args.bulkImportConfig.timeframeDays} days`);
        
        // Calculate start time based on timeframe
        const startTime = new Date(Date.now() - (args.bulkImportConfig.timeframeDays * 24 * 60 * 60 * 1000));
        
        // Call the Twitter scraper action for bulk import
        const importResult = await ctx.runAction(api.twitterScraper.searchMentionsForAccount, {
          handle: args.handle,
          maxResults: args.bulkImportConfig.maxMentions,
          startTime: startTime.toISOString(),
          enableViralDetection: args.bulkImportConfig.enableViralDetection || false,
          priorityMode: args.bulkImportConfig.priorityMode || false,
        });
        
        mentionsImported = importResult.mentionsStored || 0;
        importStatus = mentionsImported > 0 ? "success" : "no_mentions_found";
        
        console.log(`✅ Bulk import completed for @${args.handle} - ${mentionsImported} mentions imported`);
        
        // Update the account with import statistics
        await ctx.db.patch(accountId, {
          lastScrapedAt: Date.now(),
          updatedAt: Date.now(),
        });
        
      } catch (error) {
        console.error(`❌ Bulk import failed for @${args.handle}:`, error);
        importStatus = "import_error";
        // Don't throw error here - account was created successfully, just import failed
      }
    }
    
    return {
      account,
      mentionsImported,
      importStatus,
    };
  },
});