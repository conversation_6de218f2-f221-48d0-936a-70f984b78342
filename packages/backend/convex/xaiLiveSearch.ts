import { action } from "./_generated/server";
import { v } from "convex/values";
import { api } from "./_generated/api";
import { createXAIClient, getRecentDateRange } from "./lib/xai_client";
import type { ActionCtx } from "./_generated/server";

/**
 * xAI Live Search Actions for BuddyChip Pro
 * 
 * These actions provide real-time Twitter/X monitoring and analysis
 * using xAI's Grok models with native X data access.
 */

// --- Types for xAI Live Search ---
interface XAIContentAnalysisResult {
  worthinessScore: number;
  reasoning: string;
  suggestedStrategy: string;
  liveContext: string;
  citations: unknown[];
}

interface XAIResponseResult {
  suggestedResponse: string;
  alternativeResponses: string[];
  confidence: number;
  liveContext: string;
  citations: unknown[];
}

interface XAICompetitorMonitoringResult {
  insights: string;
  competitorActivity: unknown[];
  opportunities: string[];
  citations: unknown[];
}

interface XAIEnhancedMention {
  priority: "high" | "medium" | "low";
  [key: string]: unknown;
}

export const xaiLiveSearchMentions = action({
  args: {
    handles: v.array(v.string()),
    hoursBack: v.optional(v.number()),
    maxResults: v.optional(v.number()),
    additionalContext: v.optional(v.string()),
  },
  handler: async (ctx: ActionCtx, args: { handles: string[]; hoursBack?: number; maxResults?: number; additionalContext?: string }): Promise<{
    success: boolean;
    searchId?: string;
    content?: string;
    citations?: unknown[];
    mentionsFound: number;
    handles: string[];
    error?: string;
  }> => {
    // 🔐 SECURITY: Require authentication for expensive xAI operations
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Authentication required");
    }

    const user = await ctx.runQuery(api.userQueries.getCurrentUser);
    if (!user) {
      throw new Error("User not found");
    }

    // 🔐 SECURITY: Input validation and limits
    if (args.handles.length === 0) {
      throw new Error("At least one handle is required");
    }
    if (args.handles.length > 5) {
      throw new Error("Maximum 5 handles allowed per search");
    }
    const maxResults = Math.min(Math.max(args.maxResults || 20, 1), 50);
    const hoursBack = Math.min(Math.max(args.hoursBack || 24, 1), 168); // Max 7 days

    try {
      const client = createXAIClient();
      const dateRange = getRecentDateRange(hoursBack);
      
      const result = await client.searchMentions(args.handles, {
        dateRange,
        maxResults: args.maxResults || 20,
        additionalContext: args.additionalContext
      });

      // Store the search results for analysis
      const searchId = await ctx.runMutation(api.helperMutations.storeXAISearchResult, {
        searchType: "mentions",
        query: `Mentions of: ${args.handles.join(', ')}`,
        content: result.content,
        citations: result.citations,
        createdAt: Date.now(),
      });

      return {
        success: true,
        searchId,
        content: result.content,
        citations: result.citations,
        mentionsFound: Array.isArray(result.relevantMentions) ? result.relevantMentions.length : 0,
        handles: args.handles,
      };
    } catch (error) {
      console.error("xAI mention search failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        handles: args.handles,
        mentionsFound: 0,
      };
    }
  },
});

export const xaiSearchTrendingTopics = action({
  args: {
    topics: v.array(v.string()),
    sources: v.optional(v.array(v.string())),
    hoursBack: v.optional(v.number()),
    maxResults: v.optional(v.number()),
  },
  handler: async (ctx: ActionCtx, args: { topics: string[]; sources?: string[]; hoursBack?: number; maxResults?: number }): Promise<{
    success: boolean;
    searchId?: string;
    content?: string;
    citations?: unknown[];
    trendsFound: number;
    topics: string[];
    error?: string;
  }> => {
    // 🔐 SECURITY: Require authentication for expensive xAI operations
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Authentication required");
    }

    const user = await ctx.runQuery(api.userQueries.getCurrentUser);
    if (!user) {
      throw new Error("User not found");
    }

    // 🔐 SECURITY: Input validation and limits
    if (args.topics.length === 0) {
      throw new Error("At least one topic is required");
    }
    if (args.topics.length > 3) {
      throw new Error("Maximum 3 topics allowed per search");
    }
    const maxResults = Math.min(Math.max(args.maxResults || 15, 1), 25);
    const hoursBack = Math.min(Math.max(args.hoursBack || 12, 1), 72); // Max 3 days

    try {
      const client = createXAIClient();
      const dateRange = getRecentDateRange(hoursBack);
      
      const result = await client.searchTrendingTopics(args.topics, {
        dateRange,
        sources: args.sources as ("web" | "x" | "news")[] || ["x", "web", "news"],
        maxResults: args.maxResults || 15,
      });

      const searchId = await ctx.runMutation(api.helperMutations.storeXAISearchResult, {
        searchType: "trends",
        query: `Trending topics: ${args.topics.join(', ')}`,
        content: result.content,
        citations: result.citations,
        createdAt: Date.now(),
      });

      return {
        success: true,
        searchId,
        content: result.content,
        citations: result.citations,
        trendsFound: Array.isArray(result.trends) ? result.trends.length : 0,
        topics: args.topics,
      };
    } catch (error) {
      console.error("xAI trend search failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        topics: args.topics,
        trendsFound: 0,
      };
    }
  },
});

export const xaiAnalyzeContentWorthiness = action({
  args: {
    content: v.string(),
    userExpertise: v.array(v.string()),
    userInterests: v.array(v.string()),
    userBrand: v.optional(v.string()),
  },
  handler: async (ctx: ActionCtx, args: { content: string; userExpertise: string[]; userInterests: string[]; userBrand?: string }): Promise<{
    success: boolean;
    analysisId?: string;
    worthinessScore: number;
    reasoning: string;
    suggestedStrategy: string;
    liveContext?: string;
    citations?: unknown[];
    error?: string;
  }> => {
    try {
      const client = createXAIClient();
      
      const result = await client.analyzeContentWorthiness(args.content, {
        expertise: args.userExpertise,
        interests: args.userInterests,
        brand: args.userBrand,
      });

      const analysisId = await ctx.runMutation(api.helperMutations.storeXAISearchResult, {
        searchType: "worthiness_analysis",
        query: `Content analysis: ${args.content.substring(0, 100)}...`,
        content: JSON.stringify({
          worthinessScore: result.worthinessScore,
          reasoning: result.reasoning,
          suggestedStrategy: result.suggestedStrategy,
          liveContext: result.liveContext
        }),
        citations: result.citations.map((citation: unknown) => String(citation)),
        metadata: {
          analysisType: "worthiness",
          score: result.worthinessScore
        }
      });

      return {
        success: true,
        analysisId,
        worthinessScore: result.worthinessScore,
        reasoning: result.reasoning,
        suggestedStrategy: result.suggestedStrategy,
        liveContext: result.liveContext,
        citations: result.citations,
      };
    } catch (error) {
      console.error("xAI worthiness analysis failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        worthinessScore: 0,
        reasoning: "Analysis failed",
        suggestedStrategy: "none",
      };
    }
  },
});

export const xaiGenerateContextualResponse = action({
  args: {
    originalContent: v.string(),
    responseType: v.union(v.literal("reply"), v.literal("quote"), v.literal("comment")),
    userExpertise: v.array(v.string()),
    userInterests: v.array(v.string()),
    userBrand: v.optional(v.string()),
    userTone: v.optional(v.string()),
    targetAudience: v.optional(v.string()),
    strategy: v.optional(v.string()),
  },
  handler: async (ctx: ActionCtx, args: { originalContent: string; responseType: "reply" | "quote" | "comment"; userExpertise: string[]; userInterests: string[]; userBrand?: string; userTone?: string; targetAudience?: string; strategy?: string }): Promise<{
    success: boolean;
    responseId?: string;
    suggestedResponse: string;
    alternativeResponses: string[];
    confidence: number;
    liveContext?: string;
    citations?: unknown[];
    error?: string;
  }> => {
    try {
      const client = createXAIClient();
      
      const result = await client.generateContextualResponse(args.originalContent, {
        responseType: args.responseType,
        userProfile: {
          expertise: args.userExpertise,
          interests: args.userInterests,
          brand: args.userBrand,
          tone: args.userTone,
        },
        targetAudience: args.targetAudience,
        strategy: args.strategy,
      });

      const responseId = await ctx.runMutation(api.helperMutations.storeXAISearchResult, {
        searchType: "response_generation",
        query: `Response for: ${args.originalContent.substring(0, 100)}...`,
        content: JSON.stringify({
          suggestedResponse: result.suggestedResponse,
          alternativeResponses: result.alternativeResponses,
          confidence: result.confidence,
          liveContext: result.liveContext
        }),
        citations: result.citations.map((citation: unknown) => String(citation)),
        metadata: {
          responseType: args.responseType,
          confidence: result.confidence
        }
      });

      return {
        success: true,
        responseId,
        suggestedResponse: result.suggestedResponse,
        alternativeResponses: result.alternativeResponses,
        confidence: result.confidence,
        liveContext: result.liveContext,
        citations: result.citations,
      };
    } catch (error) {
      console.error("xAI response generation failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        suggestedResponse: "",
        alternativeResponses: [],
        confidence: 0,
      };
    }
  },
});

export const xaiMonitorCompetitors = action({
  args: {
    competitors: v.array(v.string()),
    industry: v.string(),
    hoursBack: v.optional(v.number()),
    focus: v.optional(v.string()),
  },
  handler: async (ctx: ActionCtx, args: { competitors: string[]; industry: string; hoursBack?: number; focus?: string }): Promise<{
    success: boolean;
    monitoringId?: string;
    insights?: string;
    competitorActivity?: unknown[];
    opportunities?: string[];
    citations?: unknown[];
    competitorsMonitored: number;
    error?: string;
  }> => {
    try {
      const client = createXAIClient();
      const dateRange = getRecentDateRange(args.hoursBack || 48);
      
      const result = await client.monitorCompetitors(args.competitors, args.industry, {
        dateRange,
        focus: args.focus,
      });

      const monitoringId = await ctx.runMutation(api.helperMutations.storeXAISearchResult, {
        searchType: "competitor_monitoring",
        query: `Competitors: ${args.competitors.join(', ')} in ${args.industry}`,
        content: JSON.stringify({
          insights: result.insights,
          competitorActivity: result.competitorActivity,
          opportunities: result.opportunities
        }),
        citations: result.citations.map((citation: unknown) => String(citation)),
        metadata: {
          competitors: args.competitors,
          industry: args.industry
        }
      });

      return {
        success: true,
        monitoringId,
        insights: result.insights,
        competitorActivity: result.competitorActivity,
        opportunities: result.opportunities,
        citations: result.citations,
        competitorsMonitored: args.competitors.length,
      };
    } catch (error) {
      console.error("xAI competitor monitoring failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        competitorsMonitored: 0,
      };
    }
  },
});

export const xaiRealTimeContentAnalysis = action({
  args: {
    query: v.string(),
    searchSources: v.optional(v.array(v.string())),
    maxResults: v.optional(v.number()),
    analysisType: v.optional(v.string()),
  },
  handler: async (ctx: ActionCtx, args: { query: string; searchSources?: string[]; maxResults?: number; analysisType?: string }): Promise<{
    success: boolean;
    analysisId?: string;
    content: string;
    citations: unknown[];
    tokensUsed: number;
    model?: string;
    error?: string;
  }> => {
    try {
      const client = createXAIClient();
      
      // Custom live search with specific analysis focus
      const searchRequest = {
        messages: [
          {
            role: "system" as const,
            content: `You are an expert social media analyst. Analyze real-time content for: ${args.analysisType || 'engagement opportunities, trends, and strategic insights'}. Provide structured analysis with actionable recommendations.`
          },
          {
            role: "user" as const,
            content: args.query
          }
        ],
        model: "grok-3-latest",
        search_parameters: {
          mode: "on" as const,
          sources: (args.searchSources || ["x", "web"]).map(type => ({ type: type as "web" | "x" | "news" | "rss" })),
          max_search_results: args.maxResults || 15,
          return_citations: true
        },
        temperature: 0.3,
        max_tokens: 2000
      };

      const result = await client.liveSearch(searchRequest);
      
      const analysisId = await ctx.runMutation(api.helperMutations.storeXAISearchResult, {
        searchType: "real_time_analysis",
        query: args.query,
        content: result.choices[0]?.message.content || "",
        citations: (result.citations || []).map((citation: unknown) => String(citation)),
        metadata: {
          analysisType: args.analysisType || "general",
          tokensUsed: result.usage?.total_tokens || 0,
          model: result.model
        }
      });

      return {
        success: true,
        analysisId,
        content: result.choices[0]?.message.content || "",
        citations: result.citations || [],
        tokensUsed: result.usage?.total_tokens || 0,
        model: result.model,
      };
    } catch (error) {
      console.error("xAI real-time analysis failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        content: "",
        citations: [],
        tokensUsed: 0,
      };
    }
  },
});

export const xaiEnhancedMentionMonitoring = action({
  args: {
    monitoredHandles: v.array(v.string()),
    keywordFilters: v.optional(v.array(v.string())),
    sentimentFilter: v.optional(v.string()),
    minEngagement: v.optional(v.number()),
    hoursBack: v.optional(v.number()),
  },
  handler: async (ctx: ActionCtx, args: { monitoredHandles: string[]; keywordFilters?: string[]; sentimentFilter?: string; minEngagement?: number; hoursBack?: number }): Promise<{
    success: boolean;
    monitoringId?: string;
    mentionsFound: number;
    highPriorityMentions: number;
    rawAnalysis?: string;
    citations?: unknown[];
    error?: string;
  }> => {
    try {
      const client = createXAIClient();
      const dateRange = getRecentDateRange(args.hoursBack || 6);
      
      const enhancedQuery = `
        Monitor mentions of ${args.monitoredHandles.map(h => `@${h.replace(/^@/, '')}`).join(', ')} with enhanced filtering:
        ${args.keywordFilters?.length ? `Keywords: ${args.keywordFilters.join(', ')}` : ''}
        ${args.sentimentFilter ? `Sentiment filter: ${args.sentimentFilter}` : ''}
        ${args.minEngagement ? `Minimum engagement: ${args.minEngagement}` : ''}
        
        Analyze each mention for:
        1. Priority level (high/medium/low)
        2. Sentiment and emotional tone
        3. Engagement potential
        4. Response urgency
        5. Strategic value
        
        Provide structured data for automated processing.
      `;

      const result = await client.searchMentions(args.monitoredHandles, {
        dateRange,
        maxResults: 30,
        additionalContext: enhancedQuery
      });

      // Parse and store enhanced mention data
      const enhancedMentions = await parseEnhancedMentions(result.content);
      
      const monitoringId = await ctx.runMutation(api.helperMutations.storeXAISearchResult, {
        searchType: "enhanced_mention_monitoring",
        query: `Enhanced monitoring: ${args.monitoredHandles.join(', ')}`,
        content: JSON.stringify({
          mentions: enhancedMentions,
          filters: {
            keywords: args.keywordFilters,
            sentiment: args.sentimentFilter,
            minEngagement: args.minEngagement,
          },
          rawAnalysis: result.content
        }),
        citations: result.citations.map((citation: unknown) => String(citation)),
        metadata: {
          monitoredHandles: args.monitoredHandles,
          mentionsFound: enhancedMentions.length
        }
      });

      return {
        success: true,
        monitoringId,
        mentionsFound: Array.isArray(enhancedMentions) ? enhancedMentions.length : 0,
        highPriorityMentions: Array.isArray(enhancedMentions)
          ? enhancedMentions.filter((m: XAIEnhancedMention) => m.priority === "high").length
          : 0,
        rawAnalysis: result.content,
        citations: result.citations,
      };
    } catch (error) {
      console.error("xAI enhanced mention monitoring failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        mentionsFound: 0,
        highPriorityMentions: 0,
      };
    }
  },
});

// --- Helper outside action for parsing enhanced mentions ---
export async function parseEnhancedMentions(content: string): Promise<XAIEnhancedMention[]> {
  // Parse the xAI response to extract structured mention data
  // This would use pattern matching or additional AI calls to structure the data
  console.log(`Parsing enhanced mentions from content: ${content.substring(0, 100)}...`);

  // TODO: Implement actual parsing logic
  return [];
}

export const xaiGetSearchHistory = action({
  args: {
    searchType: v.optional(v.string()),
    limit: v.optional(v.number()),
    since: v.optional(v.number()),
  },
  handler: async (ctx: ActionCtx, args: { searchType?: string; limit?: number; since?: number }): Promise<{
    success: boolean;
    searches: unknown[];
    totalFound: number;
    error?: string;
  }> => {
    try {
      const searches = await ctx.runQuery(api.userQueries.getXAISearchHistory, {
        searchType: args.searchType,
        limit: args.limit || 20,
        since: args.since,
      });

      return {
        success: true,
        searches,
        totalFound: searches.length,
      };
    } catch (error) {
      console.error("Failed to get xAI search history:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        searches: [],
        totalFound: 0,
      };
    }
  },
});