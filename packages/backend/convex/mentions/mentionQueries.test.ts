import { test, expect } from "bun:test";
import { convexTest } from "convex-test";
import { api } from "../_generated/api";

test("getMentionStats returns valid structure", async () => {
  const t = convexTest();
  
  // Create a test user
  const userId = await t.run(async (ctx) => {
    return await ctx.db.insert("users", {
      clerkId: "test_user",
      name: "Test User",
      email: "<EMAIL>",
    });
  });

  // Create a test Twitter account
  const accountId = await t.run(async (ctx) => {
    return await ctx.db.insert("twitterAccounts", {
      userId,
      handle: "testhandle",
      name: "Test Account",
      followers: 1000,
      isVerified: false,
      profileImage: "https://example.com/profile.jpg",
      isMonitored: true,
      lastMentionCheck: Date.now(),
    });
  });

  // Create a test mention
  await t.run(async (ctx) => {
    return await ctx.db.insert("mentions", {
      monitoredAccountId: accountId,
      mentionTweetId: "**********",
      mentionContent: "Great post! @testhandle",
      mentionAuthor: "Test Mention Author",
      mentionAuthorHandle: "mentionauthor",
      mentionAuthorFollowers: 500,
      mentionAuthorVerified: false,
      mentionType: "mention",
      engagement: {
        likes: 10,
        retweets: 5,
        replies: 2,
      },
      priority: "medium",
      isProcessed: false,
      isNotificationSent: false,
      createdAt: Date.now(),
      discoveredAt: Date.now(),
      url: "https://twitter.com/mentionauthor/status/**********",
    });
  });

  // Mock authentication context
  const stats = await t.run(async (ctx) => {
    // Override auth for testing
    ctx.auth = {
      getUserIdentity: () => Promise.resolve({ subject: "test_user" }),
    };
    
    return await api.mentions.mentionQueries.getMentionStats(ctx, {});
  });

  // Verify the structure matches our return validator
  expect(stats).toMatchObject({
    total: expect.any(Number),
    unread: expect.any(Number),
    unprocessed: expect.any(Number),
    todayCount: expect.any(Number),
    weekCount: expect.any(Number),
    responseOpportunities: expect.any(Number),
    priorityBreakdown: {
      high: expect.any(Number),
      medium: expect.any(Number),
      low: expect.any(Number),
    },
    mentionTypes: {
      mention: expect.any(Number),
      reply: expect.any(Number),
      quote: expect.any(Number),
      retweet_with_comment: expect.any(Number),
    },
  });

  // Verify we have at least one mention
  expect(stats.total).toBeGreaterThan(0);
});

test("getRecentMentions returns newest mentions first", async () => {
  const t = convexTest();
  
  // Create test data
  const userId = await t.run(async (ctx) => {
    return await ctx.db.insert("users", {
      clerkId: "test_user_2",
      name: "Test User 2",
      email: "<EMAIL>",
    });
  });

  const accountId = await t.run(async (ctx) => {
    return await ctx.db.insert("twitterAccounts", {
      userId,
      handle: "testhandle2",
      name: "Test Account 2",
      followers: 2000,
      isVerified: true,
      profileImage: "https://example.com/profile2.jpg",
      isMonitored: true,
      lastMentionCheck: Date.now(),
    });
  });

  // Create multiple mentions with different timestamps
  const now = Date.now();
  const mentions = [];
  for (let i = 0; i < 3; i++) {
    const mentionId = await t.run(async (ctx) => {
      return await ctx.db.insert("mentions", {
        monitoredAccountId: accountId,
        mentionTweetId: `mention_${i}`,
        mentionContent: `Test mention ${i}`,
        mentionAuthor: `Author ${i}`,
        mentionAuthorHandle: `author${i}`,
        mentionAuthorFollowers: 100 * i,
        mentionAuthorVerified: false,
        mentionType: "mention",
        engagement: {
          likes: i * 2,
          retweets: i,
          replies: 0,
        },
        priority: "low",
        isProcessed: false,
        isNotificationSent: false,
        createdAt: now - (i * 1000), // Each mention 1 second apart
        discoveredAt: now - (i * 1000),
        url: `https://twitter.com/author${i}/status/mention_${i}`,
      });
    });
    mentions.push(mentionId);
  }

  // Query recent mentions
  const result = await t.run(async (ctx) => {
    // Mock authentication
    ctx.auth = {
      getUserIdentity: () => Promise.resolve({ subject: "test_user_2" }),
    };
    
    return await api.mentions.mentionQueries.getRecentMentions(ctx, { limit: 5 });
  });

  // Verify structure
  expect(result).toMatchObject({
    data: expect.any(Array),
    nextCursor: expect.any(String | null),
    hasMore: expect.any(Boolean),
  });

  // Verify mentions are returned newest first
  expect(result.data.length).toBeGreaterThan(0);
  if (result.data.length > 1) {
    expect(result.data[0].createdAt).toBeGreaterThanOrEqual(result.data[1].createdAt);
  }
});