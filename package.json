{"name": "BuddyChipAI", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "lint": "turbo lint", "test": "bun test", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:server": "turbo -F @BuddyChipAI/backend dev", "dev:setup": "turbo -F @BuddyChipAI/backend setup", "convex:dev": "convex dev", "setup:twitter": "node scripts/setup-twitter-api.js", "validate:config": "cd packages/backend && npx convex run lib/config_validator:validateEnvironmentSetup", "test:twitter-api": "cd packages/backend && npx convex run lib/config_validator:testTwitterAPIConnection"}, "dependencies": {"@auth/core": "0.37.0", "@clerk/themes": "^2.2.49", "@convex-dev/agent": "^0.1.5", "@convex-dev/auth": "^0.0.87", "@openrouter/ai-sdk-provider": "^0.7.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "ai": "^4.3.16", "vitest": "^3.2.3", "zod": "^3.25.51"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "convex": "^1.24.8", "convex-test": "^0.0.16", "eslint": "^9.28.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "turbo": "^2.4.2", "typescript": "5.3.3"}, "resolutions": {"typescript": "5.3.3"}, "packageManager": "bun@1.2.15"}