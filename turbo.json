{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**"], "env": ["CONVEX_DEPLOYMENT", "AUTH_GOOGLE_ID", "AUTH_GOOGLE_SECRET", "SITE_URL", "CLERK_SECRET_KEY", "OPENROUTER_API_KEY", "OPENAI_API_KEY", "TWEETIO_API_KEY", "XAI_API_KEY"]}, "lint": {"dependsOn": ["^lint"], "outputs": []}, "check-types": {"dependsOn": ["^check-types"], "outputs": []}, "test": {"outputs": []}, "dev": {"cache": false, "persistent": true}, "setup": {"cache": false, "persistent": true}}}