import { useQuery, useMutation } from 'convex/react';
import { api } from '@BuddyChipAI/backend';
import { useCallback, useMemo, useEffect, useRef } from 'react';

/**
 * Custom hook for subscription management
 * 🚀 MEMORY LEAK FIX: Added proper cleanup and lazy loading
 */
export function useSubscription() {
  const isMountedRef = useRef(true);
  
  // 🚀 PERFORMANCE: Lazy load subscription data only when needed
  const subscription = useQuery(api.billing.subscriptions.getUserSubscription);
  const userPermissions = useQuery(api.billing.accessControl.getUserPermissions);
  const userUsage = useQuery(api.billing.usage.getUserUsage);
  const trackUsage = useMutation(api.billing.usage.trackUsage);
  
  // 🚀 MEMORY LEAK FIX: Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Memoized subscription data
  const subscriptionData = useMemo(() => {
    if (!subscription || !userPermissions) {
      return {
        isLoading: true,
        planId: 'starter' as const,
        isActive: false,
        features: {},
        limits: {},
        usage: {},
      };
    }

    return {
      isLoading: false,
      planId: userPermissions.planId,
      isActive: subscription.isActive,
      subscription: subscription,
      features: userPermissions.features,
      limits: userPermissions.limits,
      usage: userUsage?.usage || {},
      currentPeriodEnd: subscription.currentPeriodEnd,
      cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
    };
  }, [subscription, userPermissions, userUsage]);

  // Feature access helpers
  const hasFeature = useCallback((feature: string): boolean => {
    return subscriptionData.features[feature] || false;
  }, [subscriptionData.features]);

  const hasPlan = useCallback((planId: 'starter' | 'pro' | 'enterprise'): boolean => {
    const planHierarchy = { starter: 1, pro: 2, enterprise: 3 };
    const userLevel = planHierarchy[subscriptionData.planId as keyof typeof planHierarchy];
    const requiredLevel = planHierarchy[planId];
    return userLevel >= requiredLevel;
  }, [subscriptionData.planId]);

  // Usage helpers
  const canUseFeature = useCallback((
    feature: 'aiResponses' | 'imageGenerations' | 'apiRequests' | 'bulkOperations' | 'premiumAiCalls' | 'analyticsQueries',
    amount: number = 1
  ): { canUse: boolean; remaining: number; limit: number } => {
    const limitKey = `max${feature.charAt(0).toUpperCase() + feature.slice(1)}` as keyof typeof subscriptionData.limits;
    const limit = subscriptionData.limits[limitKey] as number || 0;
    const currentUsage = subscriptionData.usage[feature] || 0;
    
    // -1 means unlimited
    if (limit === -1) {
      return { canUse: true, remaining: -1, limit: -1 };
    }
    
    const remaining = limit - currentUsage;
    const canUse = remaining >= amount;
    
    return { canUse, remaining, limit };
  }, [subscriptionData.limits, subscriptionData.usage]);

  const getRemainingUsage = useCallback((
    feature: 'aiResponses' | 'imageGenerations' | 'apiRequests' | 'bulkOperations' | 'premiumAiCalls' | 'analyticsQueries'
  ): { remaining: number; limit: number; percentage: number } => {
    const { remaining, limit } = canUseFeature(feature);
    
    if (limit === -1) {
      return { remaining: -1, limit: -1, percentage: 0 };
    }
    
    const currentUsage = subscriptionData.usage[feature] || 0;
    const percentage = limit > 0 ? (currentUsage / limit) * 100 : 0;
    
    return { remaining, limit, percentage };
  }, [canUseFeature, subscriptionData.usage]);

  // Track usage helper
  const trackFeatureUsage = useCallback(async (
    feature: 'aiResponses' | 'imageGenerations' | 'apiRequests' | 'bulkOperations' | 'premiumAiCalls' | 'analyticsQueries',
    amount: number = 1
  ) => {
    try {
      console.log(`📈 Tracking usage: ${feature} (+${amount})`);
      const result = await trackUsage({ feature, amount });
      console.log(`✅ Usage tracked: ${feature} = ${result.newUsage}/${result.limit}`);
      return result;
    } catch (error) {
      console.error(`❌ Failed to track usage for ${feature}:`, error);
      throw error;
    }
  }, [trackUsage]);

  // Plan comparison helpers
  const getUpgradePath = useCallback((): { 
    nextPlan: 'pro' | 'enterprise' | null; 
    benefits: string[]; 
  } => {
    switch (subscriptionData.planId) {
      case 'starter':
        return {
          nextPlan: 'pro',
          benefits: [
            'Premium AI models (GPT-4, Claude)',
            '500 AI responses/month',
            'Image generation (50/month)',
            'Advanced analytics',
            'Priority support'
          ]
        };
      case 'pro':
        return {
          nextPlan: 'enterprise',
          benefits: [
            'Unlimited AI responses',
            'Unlimited image generation',
            'Bulk processing',
            'Custom integrations',
            'White-label options',
            '24/7 priority support'
          ]
        };
      case 'enterprise':
        return {
          nextPlan: null,
          benefits: []
        };
      default:
        return { nextPlan: null, benefits: [] };
    }
  }, [subscriptionData.planId]);

  const isNearLimit = useCallback((
    feature: 'aiResponses' | 'imageGenerations' | 'apiRequests' | 'bulkOperations' | 'premiumAiCalls' | 'analyticsQueries',
    threshold: number = 80
  ): boolean => {
    const { percentage } = getRemainingUsage(feature);
    return percentage >= threshold;
  }, [getRemainingUsage]);

  const isAtLimit = useCallback((
    feature: 'aiResponses' | 'imageGenerations' | 'apiRequests' | 'bulkOperations' | 'premiumAiCalls' | 'analyticsQueries'
  ): boolean => {
    const { canUse } = canUseFeature(feature);
    return !canUse;
  }, [canUseFeature]);

  // Subscription status helpers
  const isTrialing = useMemo(() => {
    return subscription?.trialEnd && subscription.trialEnd > Date.now();
  }, [subscription]);

  const daysUntilRenewal = useMemo(() => {
    if (!subscription?.currentPeriodEnd) return null;
    const days = Math.ceil((subscription.currentPeriodEnd - Date.now()) / (1000 * 60 * 60 * 24));
    return Math.max(0, days);
  }, [subscription]);

  const willCancelAtPeriodEnd = useMemo(() => {
    return subscription?.cancelAtPeriodEnd || false;
  }, [subscription]);

  return {
    // Core data
    ...subscriptionData,
    
    // Feature access
    hasFeature,
    hasPlan,
    
    // Usage management
    canUseFeature,
    getRemainingUsage,
    trackFeatureUsage,
    isNearLimit,
    isAtLimit,
    
    // Plan management
    getUpgradePath,
    
    // Subscription status
    isTrialing,
    daysUntilRenewal,
    willCancelAtPeriodEnd,
    
    // Convenience flags
    isPro: subscriptionData.planId === 'pro',
    isEnterprise: subscriptionData.planId === 'enterprise',
    isStarter: subscriptionData.planId === 'starter',
    
    // Feature flags
    canUsePremiumAI: hasFeature('premiumAi'),
    canGenerateImages: hasFeature('imageGeneration'),
    canUseBulkProcessing: hasFeature('bulkProcessing'),
    canUseAdvancedAnalytics: hasFeature('advancedAnalytics'),
    hasPrioritySupport: hasFeature('prioritySupport'),
    canUseCustomIntegrations: hasFeature('customIntegrations'),
    hasWhiteLabel: hasFeature('whiteLabel'),
  };
}

/**
 * Hook for checking specific feature access
 */
export function useFeatureAccess(feature: string) {
  const featureAccess = useQuery(
    api.billing.accessControl.hasFeatureAccess,
    { feature: feature as any }
  );

  return {
    hasAccess: featureAccess?.hasAccess || false,
    isLoading: featureAccess === undefined,
    planId: featureAccess?.planId,
    reason: featureAccess?.reason,
  };
}

/**
 * Hook for checking plan access
 */
export function usePlanAccess(planId: 'starter' | 'pro' | 'enterprise') {
  const planAccess = useQuery(
    api.billing.accessControl.hasPlanAccess,
    { planId }
  );

  return {
    hasAccess: planAccess?.hasAccess || false,
    isLoading: planAccess === undefined,
    userPlanId: planAccess?.userPlanId,
    requiredPlanId: planAccess?.requiredPlanId,
    reason: planAccess?.reason,
  };
}

/**
 * Hook for usage tracking with automatic error handling
 */
export function useUsageTracker() {
  const trackUsage = useMutation(api.billing.usage.trackUsage);
  const { canUseFeature } = useSubscription();

  const trackWithCheck = useCallback(async (
    feature: 'aiResponses' | 'imageGenerations' | 'apiRequests' | 'bulkOperations' | 'premiumAiCalls' | 'analyticsQueries',
    amount: number = 1
  ) => {
    // Check if user can perform the action
    const { canUse, remaining, limit } = canUseFeature(feature, amount);
    
    if (!canUse) {
      throw new Error(`Usage limit exceeded for ${feature}. Used ${limit - remaining}/${limit}.`);
    }

    // Track the usage
    try {
      const result = await trackUsage({ feature, amount });
      return {
        success: true,
        newUsage: result.newUsage,
        limit: result.limit,
        remaining: result.limit - result.newUsage,
      };
    } catch (error) {
      console.error(`Failed to track usage for ${feature}:`, error);
      throw error;
    }
  }, [trackUsage, canUseFeature]);

  return {
    trackUsage: trackWithCheck,
    canUseFeature,
  };
}
