import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  ImageIcon, 
  Sparkles, 
  Download, 
  Copy, 
  Heart,
  Share2,
  AlertCircle,
  Wand2,
  <PERSON><PERSON>,
  Monitor
} from "lucide-react";
import { useMutation } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { toast } from "sonner";

export interface ImageGeneratorProps {
  className?: string;
}

interface GeneratedImage {
  url?: string;
  base64?: string;
  revisedPrompt?: string;
  model: string;
  platform?: string;
  style?: string;
  generatedAt: number;
}

export function ImageGenerator({ className }: ImageGeneratorProps) {
  const [prompt, setPrompt] = useState("");
  const [selectedStyle, setSelectedStyle] = useState<"minimal" | "vibrant" | "professional" | "artistic">("professional");
  const [selectedPlatform, setSelectedPlatform] = useState<"twitter" | "instagram" | "linkedin">("twitter");
  const [selectedSize, setSelectedSize] = useState<"1024x1024" | "1792x1024" | "1024x1792">("1792x1024");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImages, setGeneratedImages] = useState<GeneratedImage[]>([]);
  const [activeTab, setActiveTab] = useState("generate");

  const generateImage = useMutation(api.imageGeneration.generateSocialMediaImage);
  const generateVariations = useMutation(api.imageGeneration.generateImageVariations);
  const testImageGeneration = useMutation(api.imageGeneration.testImageGeneration);

  const handleGenerateImage = async (variationCount: number = 1) => {
    if (!prompt.trim()) {
      toast.error("Please enter a description for your image");
      return;
    }

    setIsGenerating(true);
    try {
      if (variationCount > 1) {
        const result = await generateVariations({
          prompt: prompt.trim(),
          count: variationCount,
          model: "dall-e-3",
          size: selectedSize,
        });
        
        const newImages = result.variations.map((variation: any) => ({
          url: variation.url,
          base64: variation.base64,
          revisedPrompt: variation.revisedPrompt,
          model: variation.model,
          platform: selectedPlatform,
          style: selectedStyle,
          generatedAt: Date.now(),
        }));
        
        setGeneratedImages(prev => [...newImages, ...prev]);
        toast.success(`Generated ${result.variations.length} image variations!`);
      } else {
        const result = await generateImage({
          description: prompt.trim(),
          platform: selectedPlatform,
          style: selectedStyle,
          aspectRatio: selectedSize === "1792x1024" ? "landscape" : selectedSize === "1024x1792" ? "portrait" : "square",
        });
        
        const newImage: GeneratedImage = {
          url: result.url,
          base64: result.base64,
          revisedPrompt: result.revisedPrompt,
          model: result.model,
          platform: selectedPlatform,
          style: selectedStyle,
          generatedAt: Date.now(),
        };
        
        setGeneratedImages(prev => [newImage, ...prev]);
        toast.success("Image generated successfully!");
      }
    } catch (error) {
      console.error('Image generation failed:', error);
      toast.error("Failed to generate image. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleTestConnection = async () => {
    setIsGenerating(true);
    try {
      const result = await testImageGeneration({
        testPrompt: "A simple colorful abstract pattern for testing"
      });
      
      if (result.success) {
        toast.success("Image generation is working correctly!");
        if (result.imageUrl) {
          const testImage: GeneratedImage = {
            url: result.imageUrl,
            model: result.model || "dall-e-3",
            platform: "test",
            style: "test",
            generatedAt: Date.now(),
          };
          setGeneratedImages(prev => [testImage, ...prev]);
        }
      } else {
        toast.error(`Test failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Test failed:', error);
      toast.error("Test failed. Please check your API configuration.");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCopyImageUrl = (imageUrl: string) => {
    navigator.clipboard.writeText(imageUrl);
    toast.success("Image URL copied to clipboard!");
  };

  const handleDownloadImage = async (imageUrl: string, prompt: string) => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `generated-image-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success("Image downloaded!");
    } catch (error) {
      console.error('Download failed:', error);
      toast.error("Failed to download image");
    }
  };

  const styleOptions = [
    { value: "minimal", label: "Minimal", description: "Clean, simple design" },
    { value: "vibrant", label: "Vibrant", description: "Bold, colorful, energetic" },
    { value: "professional", label: "Professional", description: "Business-appropriate" },
    { value: "artistic", label: "Artistic", description: "Creative, unique perspective" },
  ];

  const platformOptions = [
    { value: "twitter", label: "Twitter/X", description: "Optimized for tweets" },
    { value: "instagram", label: "Instagram", description: "Perfect for IG posts" },
    { value: "linkedin", label: "LinkedIn", description: "Professional networking" },
  ];

  const sizeOptions = [
    { value: "1024x1024", label: "Square (1024×1024)", description: "Perfect for Instagram" },
    { value: "1792x1024", label: "Landscape (1792×1024)", description: "Great for Twitter" },
    { value: "1024x1792", label: "Portrait (1024×1792)", description: "Stories format" },
  ];

  return (
    <div className={className}>
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 bg-[var(--buddychip-grey-stroke)]/30">
          <TabsTrigger value="generate" className="data-[state=active]:bg-[var(--buddychip-accent)]">
            Generate Images
          </TabsTrigger>
          <TabsTrigger value="gallery" className="data-[state=active]:bg-[var(--buddychip-accent)]">
            Gallery ({generatedImages.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="generate" className="space-y-6">
          <Card className="bg-[var(--buddychip-black)]/80 backdrop-blur-sm border border-[var(--buddychip-grey-stroke)]/50">
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-[var(--buddychip-white)] flex items-center">
                <Wand2 className="h-5 w-5 mr-3 text-[var(--buddychip-accent)]" />
                AI Image Generator
              </CardTitle>
              <p className="text-sm text-[var(--buddychip-grey-text)]">
                Create stunning visuals for your social media posts with AI
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Image Prompt */}
              <div>
                <Label htmlFor="image-prompt" className="text-[var(--buddychip-white)] text-sm font-medium mb-2 block">
                  Image Description
                </Label>
                <Input
                  id="image-prompt"
                  placeholder="Describe the image you want to create..."
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  className="bg-[var(--buddychip-grey-stroke)]/50 border-[var(--buddychip-grey-stroke)]/50 text-[var(--buddychip-white)] focus:border-[var(--buddychip-accent)] focus:ring-[var(--buddychip-accent)]/20"
                  disabled={isGenerating}
                />
                <p className="text-xs text-[var(--buddychip-grey-text)] mt-1">
                  Be specific and detailed for best results
                </p>
              </div>

              {/* Style Selection */}
              <div>
                <Label className="text-[var(--buddychip-white)] text-sm font-medium mb-3 block">
                  <Palette className="h-4 w-4 inline mr-2" />
                  Style
                </Label>
                <div className="grid grid-cols-2 gap-3">
                  {styleOptions.map((style) => (
                    <button
                      key={style.value}
                      type="button"
                      onClick={() => setSelectedStyle(style.value as any)}
                      className={`p-3 rounded-lg border transition-all duration-200 text-left ${
                        selectedStyle === style.value
                          ? 'border-[var(--buddychip-accent)] bg-[var(--buddychip-accent)]/10'
                          : 'border-[var(--buddychip-grey-stroke)]/50 bg-[var(--buddychip-grey-stroke)]/20 hover:border-[var(--buddychip-accent)]/50'
                      }`}
                    >
                      <div className={`font-medium text-sm ${
                        selectedStyle === style.value ? 'text-[var(--buddychip-accent)]' : 'text-[var(--buddychip-white)]'
                      }`}>
                        {style.label}
                      </div>
                      <p className="text-xs text-[var(--buddychip-grey-text)] mt-1">
                        {style.description}
                      </p>
                    </button>
                  ))}
                </div>
              </div>

              {/* Platform & Size */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-[var(--buddychip-white)] text-sm font-medium mb-2 block">
                    <Monitor className="h-4 w-4 inline mr-2" />
                    Platform
                  </Label>
                  <Select value={selectedPlatform} onValueChange={(value) => setSelectedPlatform(value as "twitter" | "instagram" | "linkedin")}>
                    <SelectTrigger className="bg-[var(--buddychip-grey-stroke)]/50 border-[var(--buddychip-grey-stroke)]/50 text-[var(--buddychip-white)]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {platformOptions.map((platform) => (
                        <SelectItem key={platform.value} value={platform.value}>
                          <div>
                            <div className="font-medium">{platform.label}</div>
                            <div className="text-xs text-muted-foreground">{platform.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-[var(--buddychip-white)] text-sm font-medium mb-2 block">
                    Size
                  </Label>
                  <Select value={selectedSize} onValueChange={(value) => setSelectedSize(value as "1024x1024" | "1792x1024" | "1024x1792")}>
                    <SelectTrigger className="bg-[var(--buddychip-grey-stroke)]/50 border-[var(--buddychip-grey-stroke)]/50 text-[var(--buddychip-white)]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {sizeOptions.map((size) => (
                        <SelectItem key={size.value} value={size.value}>
                          <div>
                            <div className="font-medium">{size.label}</div>
                            <div className="text-xs text-muted-foreground">{size.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Generation Buttons */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <Button 
                  onClick={() => handleGenerateImage(1)}
                  disabled={isGenerating || !prompt.trim()}
                  className="bg-gradient-to-r from-[var(--buddychip-accent)] to-blue-500 text-[var(--buddychip-white)] hover:from-[var(--buddychip-accent)]/80 hover:to-blue-500/80"
                >
                  {isGenerating ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <ImageIcon className="h-4 w-4 mr-2" />
                  )}
                  Generate 1
                </Button>

                <Button 
                  onClick={() => handleGenerateImage(3)}
                  disabled={isGenerating || !prompt.trim()}
                  variant="outline"
                  className="border-[var(--buddychip-accent)] text-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)]/10"
                >
                  {isGenerating ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                  ) : (
                    <Sparkles className="h-4 w-4 mr-2" />
                  )}
                  3 Variations
                </Button>

                <Button 
                  onClick={handleTestConnection}
                  disabled={isGenerating}
                  variant="outline"
                  className="border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-grey-text)] hover:bg-[var(--buddychip-grey-stroke)]/20"
                >
                  Test Setup
                </Button>
              </div>

              {/* Tips */}
              <div className="bg-[var(--buddychip-accent)]/10 border border-[var(--buddychip-accent)]/20 rounded-lg p-4">
                <div className="flex items-start">
                  <AlertCircle className="h-5 w-5 text-[var(--buddychip-accent)] mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <h4 className="text-sm font-medium text-[var(--buddychip-white)] mb-2">Pro Tips</h4>
                    <ul className="text-xs text-[var(--buddychip-grey-text)] space-y-1">
                      <li>• Be specific: "A minimalist workspace with laptop and coffee" vs "workspace"</li>
                      <li>• Include mood: "cheerful", "professional", "dramatic", "calm"</li>
                      <li>• Mention colors: "blue and white", "warm earth tones", "vibrant neon"</li>
                      <li>• Specify composition: "centered", "top-down view", "close-up"</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="gallery" className="space-y-4">
          {generatedImages.length === 0 ? (
            <Card className="bg-[var(--buddychip-black)]/80 backdrop-blur-sm border border-[var(--buddychip-grey-stroke)]/50">
              <CardContent className="flex flex-col items-center justify-center py-12">
                <ImageIcon className="h-12 w-12 text-[var(--buddychip-grey-text)] mb-4" />
                <h3 className="text-lg font-medium text-[var(--buddychip-white)] mb-2">No images yet</h3>
                <p className="text-sm text-[var(--buddychip-grey-text)] text-center">
                  Generate your first image to see it appear here
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {generatedImages.map((image, index) => (
                <Card key={index} className="bg-[var(--buddychip-black)]/80 backdrop-blur-sm border border-[var(--buddychip-grey-stroke)]/50 overflow-hidden">
                  <div className="aspect-square relative overflow-hidden">
                    <img 
                      src={image.url || `data:image/png;base64,${image.base64}`}
                      alt="Generated image"
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-2 right-2 flex gap-2">
                      {image.platform && (
                        <Badge variant="secondary" className="bg-[var(--buddychip-black)]/80 text-[var(--buddychip-white)] text-xs">
                          {image.platform}
                        </Badge>
                      )}
                      {image.style && (
                        <Badge variant="secondary" className="bg-[var(--buddychip-black)]/80 text-[var(--buddychip-white)] text-xs">
                          {image.style}
                        </Badge>
                      )}
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-xs text-[var(--buddychip-grey-text)]">
                        {new Date(image.generatedAt).toLocaleDateString()}
                      </span>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => image.url && handleCopyImageUrl(image.url)}
                          className="h-8 w-8 p-0 hover:bg-[var(--buddychip-grey-stroke)]/20"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => image.url && handleDownloadImage(image.url, image.revisedPrompt || "Generated image")}
                          className="h-8 w-8 p-0 hover:bg-[var(--buddychip-grey-stroke)]/20"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0 hover:bg-[var(--buddychip-grey-stroke)]/20"
                        >
                          <Heart className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    {image.revisedPrompt && (
                      <p className="text-xs text-[var(--buddychip-grey-text)] line-clamp-2">
                        {image.revisedPrompt}
                      </p>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}