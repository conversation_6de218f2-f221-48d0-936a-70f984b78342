import { useState, useEffect, use<PERSON>emo, use<PERSON><PERSON>back, memo } from "react";
import { useAction, useQuery } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { But<PERSON> } from "../ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "../ui/tabs";
import { SearchInterface } from "./search-interface";
import { SearchResults } from "./search-results";
import { XAIInsights } from "./xai-insights";
import { SearchAnalytics } from "./search-analytics";
import { SavedSearches } from "./saved-searches";
import { 
  Search, 
  Zap, 
  TrendingUp, 
  BarChart3, 
  BookmarkIcon,
  Sparkles,
  Globe,
  MessageSquare,
  Eye,
  Clock,
  Activity,
  Brain,
  Target,
  Lightbulb
} from "lucide-react";
import { cn } from "../../lib/utils";

interface SearchResult {
  id: string;
  source: "xai" | "tweetio" | "hybrid";
  query: string;
  content?: string;
  citations?: string[];
  results?: any[];
  insights?: any;
  timestamp: number;
  success: boolean;
  error?: string;
}

export function LiveSearchDashboard() {
  const [activeTab, setActiveTab] = useState<string>("search");
  const [currentSearch, setCurrentSearch] = useState<SearchResult | null>(null);
  const [searchHistory, setSearchHistory] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [realTimeStats, setRealTimeStats] = useState({
    totalSearches: 0,
    xaiSearches: 0,
    averageResponseTime: 0,
    successRate: 100
  });

  // Actions for different search types
  const enhancedLiveSearch = useAction(api.ai.xaiLiveSearch.xaiEnhancedLiveSearch);
  const trendingSearch = useAction(api.ai.xaiLiveSearch.xaiSearchTrendingTopics);
  const mentionSearch = useAction(api.ai.xaiLiveSearch.xaiLiveSearchMentions);
  const contentAnalysis = useAction(api.ai.xaiLiveSearch.xaiRealTimeContentAnalysis);
  const fallbackSearch = useAction(api.twitterScraper.liveSearch);

  // Handle search execution
  const handleSearch = async (searchParams: {
    query: string;
    searchType: "general" | "mentions" | "trends" | "analysis";
    sources: string[];
    dateRange?: { from: string; to: string };
    maxResults?: number;
    filters?: any;
    useXAI?: boolean;
  }) => {
    setIsLoading(true);
    const startTime = Date.now();
    
    try {
      let result: SearchResult;
      
      if (searchParams.useXAI && searchParams.searchType === "general") {
        const response = await enhancedLiveSearch({
          query: searchParams.query,
          sources: searchParams.sources as any,
          dateRange: searchParams.dateRange,
          maxResults: searchParams.maxResults || 20,
          filters: searchParams.filters
        });
        
        result = {
          id: crypto.randomUUID(),
          source: "xai",
          query: searchParams.query,
          content: response.content,
          citations: response.citations,
          timestamp: Date.now(),
          success: response.success,
          error: response.error
        };
      } else if (searchParams.useXAI && searchParams.searchType === "trends") {
        const response = await trendingSearch({
          topics: [searchParams.query],
          dateRange: searchParams.dateRange,
          sources: searchParams.sources as any,
          maxResults: searchParams.maxResults || 15
        });
        
        result = {
          id: crypto.randomUUID(),
          source: "xai",
          query: searchParams.query,
          content: response.content,
          citations: response.citations,
          insights: { trends: response.trends },
          timestamp: Date.now(),
          success: response.success,
          error: response.error
        };
      } else {
        // Fallback to TweetIO
        const response = await fallbackSearch({
          query: searchParams.query,
          maxResults: searchParams.maxResults || 20,
          startTime: searchParams.dateRange?.from,
          endTime: searchParams.dateRange?.to,
          useXAI: false
        });
        
        result = {
          id: crypto.randomUUID(),
          source: "tweetio",
          query: searchParams.query,
          results: response.results,
          timestamp: Date.now(),
          success: response.success,
          error: response.error
        };
      }
      
      const responseTime = Date.now() - startTime;
      
      // Update stats
      setRealTimeStats(prev => ({
        totalSearches: prev.totalSearches + 1,
        xaiSearches: prev.xaiSearches + (result.source === "xai" ? 1 : 0),
        averageResponseTime: Math.round((prev.averageResponseTime * prev.totalSearches + responseTime) / (prev.totalSearches + 1)),
        successRate: Math.round(((prev.successRate * prev.totalSearches / 100) + (result.success ? 1 : 0)) / (prev.totalSearches + 1) * 100)
      }));
      
      setCurrentSearch(result);
      setSearchHistory(prev => [result, ...prev.slice(0, 9)]); // Keep last 10 searches
      
    } catch (error) {
      console.error("Search error:", error);
      const result: SearchResult = {
        id: crypto.randomUUID(),
        source: "tweetio",
        query: searchParams.query,
        timestamp: Date.now(),
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      };
      setCurrentSearch(result);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6 bg-gradient-to-br from-[#0E1117] via-[#0F1419] to-[#0E1117] min-h-screen relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-[#316FE3]/5 rounded-full blur-3xl animate-pulse" />
        <div className="absolute top-40 right-20 w-48 h-48 bg-purple-500/3 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute bottom-20 left-1/3 w-40 h-40 bg-cyan-500/4 rounded-full blur-3xl animate-pulse delay-2000" />
      </div>

      {/* Enhanced Header */}
      <div className="relative z-10">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
          <div className="space-y-2">
            <div className="flex items-center gap-3">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-br from-[#316FE3] to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Search className="h-6 w-6 text-white" />
                </div>
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                  <Zap className="h-2 w-2 text-white" />
                </div>
              </div>
              <div>
                <h1 className="text-3xl font-bold text-[#F5F7FA] tracking-tight">Live Twitter Search</h1>
                <p className="text-[#6E7A8C] text-sm">
                  Real-time search powered by xAI Grok with advanced analytics
                </p>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-4 text-xs text-[#6E7A8C] bg-[#000000]/40 px-4 py-2 rounded-lg border border-[#202631]">
              <div className="flex items-center gap-1">
                <Activity className="h-3 w-3 text-green-400" />
                <span>{realTimeStats.totalSearches} searches</span>
              </div>
              <div className="flex items-center gap-1">
                <Brain className="h-3 w-3 text-[#316FE3]" />
                <span>{realTimeStats.xaiSearches} xAI</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3 text-purple-400" />
                <span>{realTimeStats.averageResponseTime}ms avg</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Real-time Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 relative z-10">
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all duration-300 hover:border-[#316FE3]/50 group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#F5F7FA] group-hover:text-[#316FE3] transition-colors">Total Searches</CardTitle>
            <Search className="h-4 w-4 text-[#316FE3] group-hover:text-blue-400 transition-colors" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#F5F7FA] group-hover:text-[#316FE3] transition-colors">
              {realTimeStats.totalSearches}
            </div>
            <p className="text-xs text-[#6E7A8C]">
              Live and historical searches
            </p>
          </CardContent>
        </Card>

        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all duration-300 hover:border-purple-500/50 group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#F5F7FA] group-hover:text-purple-400 transition-colors">xAI Powered</CardTitle>
            <Brain className="h-4 w-4 text-purple-500 group-hover:text-purple-400 transition-colors" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#F5F7FA] group-hover:text-purple-400 transition-colors">
              {realTimeStats.xaiSearches}
            </div>
            <p className="text-xs text-[#6E7A8C]">
              Enhanced with live context
            </p>
          </CardContent>
        </Card>

        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all duration-300 hover:border-green-500/50 group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#F5F7FA] group-hover:text-green-400 transition-colors">Success Rate</CardTitle>
            <Target className="h-4 w-4 text-green-500 group-hover:text-green-400 transition-colors" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#F5F7FA] group-hover:text-green-400 transition-colors">
              {realTimeStats.successRate}%
            </div>
            <p className="text-xs text-[#6E7A8C]">
              Successful query execution
            </p>
          </CardContent>
        </Card>

        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all duration-300 hover:border-orange-500/50 group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#F5F7FA] group-hover:text-orange-400 transition-colors">Avg Response</CardTitle>
            <Clock className="h-4 w-4 text-orange-500 group-hover:text-orange-400 transition-colors" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#F5F7FA] group-hover:text-orange-400 transition-colors">
              {realTimeStats.averageResponseTime}ms
            </div>
            <p className="text-xs text-[#6E7A8C]">
              Real-time performance
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5 bg-[#000000]/70 border-[#202631]">
          <TabsTrigger 
            value="search" 
            className="text-[#6E7A8C] data-[state=active]:text-[#F5F7FA] data-[state=active]:bg-[#316FE3] hover:text-[#F5F7FA] transition-colors"
          >
            <Search className="h-4 w-4 mr-2" />
            Search
          </TabsTrigger>
          <TabsTrigger 
            value="results"
            className="text-[#6E7A8C] data-[state=active]:text-[#F5F7FA] data-[state=active]:bg-[#316FE3] hover:text-[#F5F7FA] transition-colors"
          >
            <MessageSquare className="h-4 w-4 mr-2" />
            Results
          </TabsTrigger>
          <TabsTrigger 
            value="insights"
            className="text-[#6E7A8C] data-[state=active]:text-[#F5F7FA] data-[state=active]:bg-[#316FE3] hover:text-[#F5F7FA] transition-colors"
          >
            <Lightbulb className="h-4 w-4 mr-2" />
            Insights
          </TabsTrigger>
          <TabsTrigger 
            value="analytics"
            className="text-[#6E7A8C] data-[state=active]:text-[#F5F7FA] data-[state=active]:bg-[#316FE3] hover:text-[#F5F7FA] transition-colors"
          >
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </TabsTrigger>
          <TabsTrigger 
            value="saved"
            className="text-[#6E7A8C] data-[state=active]:text-[#F5F7FA] data-[state=active]:bg-[#316FE3] hover:text-[#F5F7FA] transition-colors"
          >
            <BookmarkIcon className="h-4 w-4 mr-2" />
            Saved
          </TabsTrigger>
        </TabsList>

        <TabsContent value="search" className="space-y-4 relative z-10">
          <SearchInterface 
            onSearch={handleSearch}
            isLoading={isLoading}
            searchHistory={searchHistory}
          />
        </TabsContent>

        <TabsContent value="results" className="space-y-4 relative z-10">
          <SearchResults 
            currentSearch={currentSearch}
            searchHistory={searchHistory}
            onSearchSelect={setCurrentSearch}
          />
        </TabsContent>

        <TabsContent value="insights" className="space-y-4 relative z-10">
          <XAIInsights 
            currentSearch={currentSearch}
            onAnalyze={contentAnalysis}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4 relative z-10">
          <SearchAnalytics 
            searchHistory={searchHistory}
            realTimeStats={realTimeStats}
          />
        </TabsContent>

        <TabsContent value="saved" className="space-y-4 relative z-10">
          <SavedSearches 
            onSearchSelect={setCurrentSearch}
            onExecuteSearch={handleSearch}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}