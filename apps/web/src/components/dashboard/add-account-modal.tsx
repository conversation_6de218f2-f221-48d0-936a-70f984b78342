import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  X, 
  Twitter, 
  User, 
  AlertCircle, 
  Check,
  Plus,
  Settings,
  Download,
  Clock
} from "lucide-react";
import { toast } from "sonner";

interface AddAccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export function AddAccountModal({ isOpen, onClose, onSuccess }: AddAccountModalProps) {
  const [handle, setHandle] = useState("");
  const [displayName, setDisplayName] = useState("");
  const [isActive, setIsActive] = useState(true);
  const [isMonitoringEnabled, setIsMonitoringEnabled] = useState(true);
  const [isValidating, setIsValidating] = useState(false);
  const [validationStatus, setValidationStatus] = useState<'idle' | 'valid' | 'invalid'>('idle');
  
  // Bulk mention import settings
  const [enableBulkImport, setEnableBulkImport] = useState(true);
  const [mentionCount, setMentionCount] = useState("75");
  const [importTimeframe, setImportTimeframe] = useState("7"); // days

  const addTwitterAccount = useMutation(api.twitterAccounts.addTwitterAccount);
  const addAccountWithBulkImport = useMutation(api.twitterAccounts.addAccountWithBulkImport);

  const validateTwitterHandle = (inputHandle: string) => {
    // Remove @ symbol if present and clean the handle
    const cleanHandle = inputHandle.replace(/^@/, '').trim();
    
    // Basic validation: alphanumeric + underscores, 1-15 characters
    const isValid = /^[a-zA-Z0-9_]{1,15}$/.test(cleanHandle);
    
    if (isValid && cleanHandle.length > 0) {
      setValidationStatus('valid');
      setDisplayName(cleanHandle); // Auto-fill display name
    } else {
      setValidationStatus('invalid');
    }
    
    return cleanHandle;
  };

  const handleHandleChange = (value: string) => {
    const cleanHandle = validateTwitterHandle(value);
    setHandle(cleanHandle);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validationStatus !== 'valid') {
      toast.error("Please enter a valid Twitter handle");
      return;
    }

    setIsValidating(true);
    
    try {
      if (enableBulkImport) {
        // Use bulk import mutation
        const result = await addAccountWithBulkImport({
          handle,
          displayName: displayName || handle,
          isActive,
          isMonitoringEnabled,
          bulkImportConfig: {
            maxMentions: parseInt(mentionCount),
            timeframeDays: parseInt(importTimeframe),
            enableViralDetection: true,
            priorityMode: true
          }
        });
        
        if (result.mentionsImported > 0) {
          toast.success(`Twitter account @${handle} added with ${result.mentionsImported} mentions imported!`);
        } else {
          toast.success(`Twitter account @${handle} added successfully!`);
        }
      } else {
        // Standard account creation
        await addTwitterAccount({
          handle,
          displayName: displayName || handle,
          isActive,
          isMonitoringEnabled,
        });
        
        toast.success(`Twitter account @${handle} added successfully!`);
      }
      
      onSuccess?.();
      handleClose();
    } catch (error) {
      console.error("Failed to add Twitter account:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to add account";
      toast.error(errorMessage);
    } finally {
      setIsValidating(false);
    }
  };

  const handleClose = () => {
    setHandle("");
    setDisplayName("");
    setIsActive(true);
    setIsMonitoringEnabled(true);
    setValidationStatus('idle');
    setEnableBulkImport(true);
    setMentionCount("75");
    setImportTimeframe("7");
    onClose();
  };

  const getValidationIcon = () => {
    switch (validationStatus) {
      case 'valid':
        return <Check className="h-4 w-4 text-green-400" />;
      case 'invalid':
        return <AlertCircle className="h-4 w-4 text-red-400" />;
      default:
        return null;
    }
  };

  const getValidationMessage = () => {
    switch (validationStatus) {
      case 'valid':
        return <span className="text-green-400 text-sm">Valid Twitter handle</span>;
      case 'invalid':
        return <span className="text-red-400 text-sm">Invalid handle (1-15 characters, letters, numbers, underscore only)</span>;
      default:
        return <span className="text-[var(--buddychip-grey-text)] text-sm">Enter a Twitter handle (with or without @)</span>;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)] w-full max-w-md mx-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Twitter className="h-5 w-5 text-[var(--buddychip-accent)]" />
              <CardTitle className="text-[var(--buddychip-white)]">Add Twitter Account</CardTitle>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Twitter Handle Input */}
            <div className="space-y-2">
              <Label htmlFor="handle" className="text-[var(--buddychip-white)] font-medium">
                Twitter Handle
              </Label>
              <div className="relative">
                <Input
                  id="handle"
                  type="text"
                  placeholder="username"
                  value={handle}
                  onChange={(e) => handleHandleChange(e.target.value)}
                  className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] pl-8 pr-10"
                />
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[var(--buddychip-grey-text)]">
                  @
                </span>
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  {getValidationIcon()}
                </div>
              </div>
              {getValidationMessage()}
            </div>

            {/* Display Name Input */}
            <div className="space-y-2">
              <Label htmlFor="displayName" className="text-[var(--buddychip-white)] font-medium">
                Display Name
              </Label>
              <Input
                id="displayName"
                type="text"
                placeholder="Display name (optional)"
                value={displayName}
                onChange={(e) => setDisplayName(e.target.value)}
                className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)]"
              />
              <p className="text-xs text-[var(--buddychip-grey-text)]">
                Friendly name for this account (defaults to handle)
              </p>
            </div>

            <Separator className="bg-[var(--buddychip-grey-stroke)]" />

            {/* Account Settings */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-[var(--buddychip-white)] flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Account Settings
              </h4>
              
              {/* Active Status */}
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-[var(--buddychip-white)] text-sm">Active Account</Label>
                  <p className="text-xs text-[var(--buddychip-grey-text)]">
                    Enable data collection and analysis
                  </p>
                </div>
                <Switch
                  checked={isActive}
                  onCheckedChange={setIsActive}
                />
              </div>

              {/* Monitoring Status */}
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-[var(--buddychip-white)] text-sm">Mention Monitoring</Label>
                  <p className="text-xs text-[var(--buddychip-grey-text)]">
                    Monitor mentions for Reply Guy feature
                  </p>
                </div>
                <Switch
                  checked={isMonitoringEnabled}
                  onCheckedChange={setIsMonitoringEnabled}
                />
              </div>
            </div>

            <Separator className="bg-[var(--buddychip-grey-stroke)]" />

            {/* Bulk Mention Import Settings */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-[var(--buddychip-white)] flex items-center gap-2">
                <Download className="h-4 w-4" />
                Initial Mention Import
              </h4>
              
              {/* Enable Bulk Import */}
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-[var(--buddychip-white)] text-sm">Import Historical Mentions</Label>
                  <p className="text-xs text-[var(--buddychip-grey-text)]">
                    Automatically fetch recent mentions when adding this account
                  </p>
                </div>
                <Switch
                  checked={enableBulkImport}
                  onCheckedChange={setEnableBulkImport}
                />
              </div>

              {/* Bulk Import Configuration */}
              {enableBulkImport && (
                <div className="space-y-4 pl-4 border-l-2 border-[var(--buddychip-accent)]/20">
                  {/* Mention Count */}
                  <div className="space-y-2">
                    <Label className="text-[var(--buddychip-white)] text-sm">Number of Mentions</Label>
                    <Select value={mentionCount} onValueChange={setMentionCount}>
                      <SelectTrigger className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
                        <SelectItem value="25" className="text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/20">25 mentions</SelectItem>
                        <SelectItem value="50" className="text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/20">50 mentions</SelectItem>
                        <SelectItem value="75" className="text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/20">75 mentions (recommended)</SelectItem>
                        <SelectItem value="100" className="text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/20">100 mentions</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-[var(--buddychip-grey-text)]">
                      Higher counts provide more data but take longer to process
                    </p>
                  </div>

                  {/* Time Frame */}
                  <div className="space-y-2">
                    <Label className="text-[var(--buddychip-white)] text-sm">Time Frame</Label>
                    <Select value={importTimeframe} onValueChange={setImportTimeframe}>
                      <SelectTrigger className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
                        <SelectItem value="1" className="text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/20">Last 24 hours</SelectItem>
                        <SelectItem value="3" className="text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/20">Last 3 days</SelectItem>
                        <SelectItem value="7" className="text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/20">Last 7 days (recommended)</SelectItem>
                        <SelectItem value="14" className="text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/20">Last 14 days</SelectItem>
                        <SelectItem value="30" className="text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/20">Last 30 days</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-[var(--buddychip-grey-text)]">
                      Import mentions from this period
                    </p>
                  </div>

                  {/* Import Preview */}
                  <div className="p-3 bg-[var(--buddychip-accent)]/10 rounded-lg border border-[var(--buddychip-accent)]/20">
                    <div className="flex items-center gap-2 text-xs text-[var(--buddychip-accent)]">
                      <Clock className="h-3 w-3" />
                      <span>Will import up to {mentionCount} mentions from the last {importTimeframe} day{parseInt(importTimeframe) === 1 ? '' : 's'}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Preview */}
            {validationStatus === 'valid' && (
              <div className="p-3 bg-[var(--buddychip-grey-stroke)]/20 rounded-lg border border-[var(--buddychip-grey-stroke)]/30">
                <p className="text-xs text-[var(--buddychip-grey-text)] mb-2">Preview:</p>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-[var(--buddychip-grey-text)]" />
                  <span className="text-[var(--buddychip-white)] font-medium">
                    {displayName || handle}
                  </span>
                  <Badge variant="outline" className="text-[var(--buddychip-grey-text)] border-[var(--buddychip-grey-stroke)] text-xs">
                    @{handle}
                  </Badge>
                  {isActive && (
                    <Badge className="bg-green-500/20 text-green-400 border-green-500/30 text-xs">
                      Active
                    </Badge>
                  )}
                  {isMonitoringEnabled && (
                    <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30 text-xs">
                      Monitoring
                    </Badge>
                  )}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex gap-3 pt-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                className="flex-1 border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/20"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={validationStatus !== 'valid' || isValidating}
                className="flex-1 bg-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)]/80 text-[var(--buddychip-white)]"
              >
                {isValidating ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin mr-2" />
                    {enableBulkImport ? "Adding & Importing..." : "Adding..."}
                  </>
                ) : (
                  <>
                    {enableBulkImport ? (
                      <>
                        <Download className="h-4 w-4 mr-2" />
                        Add & Import {mentionCount} Mentions
                      </>
                    ) : (
                      <>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Account
                      </>
                    )}
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}