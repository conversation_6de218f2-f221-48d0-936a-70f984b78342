import { useState } from "react";
import { But<PERSON> } from "../ui/button";
import { Badge } from "../ui/badge";
import { <PERSON>, CardContent, CardHeader } from "../ui/card";
import { Textarea } from "../ui/textarea";
import { Separator } from "../ui/separator";
import { 
  Copy, 
  Edit, 
  Save, 
  X, 
  CheckCircle, 
  XCircle, 
  Sparkles, 
  Brain,
  TrendingUp,
  BarChart3,
  ExternalLink,
  Reply,
  Trash2,
  Heart,
  Repeat2
} from "lucide-react";
import { toast } from "sonner";

interface EnhancedResponse {
  _id: string;
  content: string;
  style: string;
  confidence: number;
  characterCount: number;
  model: string;
  estimatedEngagement: {
    likes: number;
    retweets: number;
    replies: number;
  };
  status: "draft" | "approved" | "declined" | "posted" | "failed";
  isEnhanced?: boolean;
  enhancementScore?: number;
  qualityMetrics?: {
    relevance: number;
    clarity: number;
    engagement: number;
    brandSafety: number;
  };
  riskAssessment?: {
    overallRisk: "low" | "medium" | "high";
    riskReasons?: string[];
  };
}

interface EnhancedResponseDisplayProps {
  responses: EnhancedResponse[];
  originalUrl?: string;
  onCopyResponse: (content: string) => void;
  onEditResponse: (responseId: string, content: string) => void;
  onApproveResponse: (responseId: string) => void;
  onDeclineResponse: (responseId: string) => void;
  onDeleteResponse: (responseId: string) => void;
  onEnhanceResponse: (responseId: string, style: string) => void;
  isEnhancing?: boolean;
}

export function EnhancedResponseDisplay({
  responses,
  originalUrl,
  onCopyResponse,
  onEditResponse,
  onApproveResponse,
  onDeclineResponse,
  onDeleteResponse,
  onEnhanceResponse,
  isEnhancing = false
}: EnhancedResponseDisplayProps) {
  const [editingResponseId, setEditingResponseId] = useState<string | null>(null);
  const [editedContent, setEditedContent] = useState<string>("");

  const handleStartEdit = (responseId: string, currentContent: string) => {
    setEditingResponseId(responseId);
    setEditedContent(currentContent);
  };

  const handleSaveEdit = (responseId: string) => {
    if (editedContent.trim() === "") {
      toast.error("Response content cannot be empty");
      return;
    }
    onEditResponse(responseId, editedContent.trim());
    setEditingResponseId(null);
    setEditedContent("");
  };

  const handleCancelEdit = () => {
    setEditingResponseId(null);
    setEditedContent("");
  };

  const handleReplyOnTwitter = (responseContent: string) => {
    if (!originalUrl) {
      toast.error("Original tweet URL not available");
      return;
    }

    const tweetIdMatch = originalUrl.match(/status\/(\d+)/);
    if (!tweetIdMatch) {
      toast.error("Could not extract tweet ID from URL");
      return;
    }
    
    const tweetId = tweetIdMatch[1];
    const twitterReplyUrl = `https://twitter.com/intent/tweet?in_reply_to=${tweetId}&text=${encodeURIComponent(responseContent)}`;
    
    window.open(twitterReplyUrl, '_blank');
    toast.success("Opening Twitter reply...");
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return "text-green-400";
    if (confidence >= 0.6) return "text-yellow-400";
    return "text-red-400";
  };

  const getQualityColor = (score: number) => {
    if (score >= 0.8) return "text-green-400";
    if (score >= 0.6) return "text-yellow-400";
    return "text-red-400";
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "low": return "text-green-400";
      case "medium": return "text-yellow-400";
      case "high": return "text-red-400";
      default: return "text-gray-400";
    }
  };

  const getEngagementTotal = (engagement: { likes: number; retweets: number; replies: number }) => {
    return engagement.likes + engagement.retweets + engagement.replies;
  };

  const formatQualityScore = (score: number) => {
    return `${Math.round(score * 100)}%`;
  };

  // Separate enhanced and regular responses
  const enhancedResponses = responses.filter(r => r.isEnhanced);
  const regularResponses = responses.filter(r => !r.isEnhanced);

  if (responses.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-[var(--buddychip-grey-text)]">
          No responses generated yet. Use the AI Response Generator to create responses.
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Responses Section */}
      {enhancedResponses.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-[var(--buddychip-accent)]" />
            <h3 className="text-lg font-semibold text-[var(--buddychip-white)]">
              Enhanced AI Responses
            </h3>
            <Badge className="bg-gradient-to-r from-[var(--buddychip-accent)] to-purple-500 text-white">
              {enhancedResponses.length} Enhanced
            </Badge>
          </div>
          
          <div className="space-y-4">
            {enhancedResponses.map((response) => (
              <Card key={response._id} className="bg-gradient-to-r from-[var(--buddychip-black)]/90 to-[var(--buddychip-accent)]/5 border-[var(--buddychip-accent)]/30 backdrop-blur-sm">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <Badge className="bg-gradient-to-r from-[var(--buddychip-accent)] to-purple-500 text-white">
                        ✨ {response.style}
                      </Badge>
                      {response.enhancementScore && (
                        <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                          +{Math.round(response.enhancementScore * 100)}% improved
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-1">
                      {originalUrl && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleReplyOnTwitter(response.content)}
                          className="text-[#1DA1F2] hover:text-[#1a8cd8] hover:bg-[#1DA1F2]/10"
                          title="Reply on Twitter"
                        >
                          <Reply className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleStartEdit(response._id, response.content)}
                        className="text-[var(--buddychip-accent)] hover:text-[var(--buddychip-accent)]/80 hover:bg-[var(--buddychip-accent)]/10"
                        title="Edit Response"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onCopyResponse(response.content)}
                        className="text-[var(--buddychip-white)] hover:text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]"
                        title="Copy Response"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onDeleteResponse(response._id)}
                        className="text-red-500 hover:text-red-400 hover:bg-red-500/10"
                        title="Delete Response"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Response Content */}
                  <div className="bg-[var(--buddychip-black)] border border-[var(--buddychip-accent)]/20 rounded-lg p-4 border-l-4 border-l-[var(--buddychip-accent)]">
                    {editingResponseId === response._id ? (
                      <div className="space-y-3">
                        <Textarea
                          value={editedContent}
                          onChange={(e) => setEditedContent(e.target.value)}
                          className="min-h-[100px] resize-none bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] focus:border-[var(--buddychip-accent)]"
                          placeholder="Edit your response..."
                        />
                        <div className="flex items-center justify-between">
                          <div className="text-xs text-[var(--buddychip-grey-text)]">
                            Length: {editedContent.length} characters
                          </div>
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              onClick={() => handleSaveEdit(response._id)}
                              className="bg-green-600 hover:bg-green-600/90 text-white"
                            >
                              <Save className="h-3 w-3 mr-1" />
                              Save
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={handleCancelEdit}
                              className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
                            >
                              <X className="h-3 w-3 mr-1" />
                              Cancel
                            </Button>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <>
                        <p className="text-sm leading-relaxed text-[var(--buddychip-white)]">{response.content}</p>
                        <div className="mt-2 flex items-center justify-between text-xs text-[var(--buddychip-grey-text)]">
                          <span>Length: {response.content.length} characters</span>
                          <span>Model: {response.model || 'GPT-4'}</span>
                        </div>
                      </>
                    )}
                  </div>

                  {/* Enhanced Metrics */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Quality Metrics */}
                    {response.qualityMetrics && (
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium text-[var(--buddychip-white)] flex items-center gap-2">
                          <BarChart3 className="h-4 w-4 text-[var(--buddychip-accent)]" />
                          Quality Metrics
                        </h4>
                        <div className="grid grid-cols-2 gap-3 text-sm">
                          <div>
                            <div className="text-[var(--buddychip-grey-text)]">Relevance</div>
                            <div className={`font-medium ${getQualityColor(response.qualityMetrics.relevance)}`}>
                              {formatQualityScore(response.qualityMetrics.relevance)}
                            </div>
                          </div>
                          <div>
                            <div className="text-[var(--buddychip-grey-text)]">Clarity</div>
                            <div className={`font-medium ${getQualityColor(response.qualityMetrics.clarity)}`}>
                              {formatQualityScore(response.qualityMetrics.clarity)}
                            </div>
                          </div>
                          <div>
                            <div className="text-[var(--buddychip-grey-text)]">Engagement</div>
                            <div className={`font-medium ${getQualityColor(response.qualityMetrics.engagement)}`}>
                              {formatQualityScore(response.qualityMetrics.engagement)}
                            </div>
                          </div>
                          <div>
                            <div className="text-[var(--buddychip-grey-text)]">Brand Safety</div>
                            <div className={`font-medium ${getQualityColor(response.qualityMetrics.brandSafety)}`}>
                              {formatQualityScore(response.qualityMetrics.brandSafety)}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Estimated Engagement */}
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-[var(--buddychip-white)] flex items-center gap-2">
                        <TrendingUp className="h-4 w-4 text-[var(--buddychip-accent)]" />
                        Predicted Engagement
                      </h4>
                    </div>
                  </div>

                  {/* Risk Assessment */}
                  {response.riskAssessment && response.riskAssessment.riskReasons && response.riskAssessment.riskReasons.length > 0 && (
                    <div className="bg-[var(--buddychip-black)] border border-yellow-600/30 rounded-lg p-3">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-sm font-medium text-yellow-400">⚠️ Risk Assessment</span>
                        <Badge variant="outline" className={`${getRiskColor(response.riskAssessment.overallRisk)} border-current text-xs`}>
                          {response.riskAssessment.overallRisk} risk
                        </Badge>
                      </div>
                      <ul className="text-xs text-yellow-300 space-y-1">
                        {response.riskAssessment.riskReasons.map((reason: string, index: number) => (
                          <li key={index}>• {reason}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Action Buttons for Draft Status */}
                  {response.status === 'draft' && (
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={() => onApproveResponse(response._id)}
                        className="flex-1 bg-green-600 hover:bg-green-600/90 text-white border-0"
                      >
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Approve
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => onDeclineResponse(response._id)}
                        className="flex-1 border border-red-600 text-red-400 hover:bg-red-600/90 hover:text-white"
                      >
                        <XCircle className="h-4 w-4 mr-2" />
                        Decline
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Regular Responses Section */}
      {regularResponses.length > 0 && (
        <>
          {enhancedResponses.length > 0 && <Separator />}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-[var(--buddychip-grey-text)]" />
                <h3 className="text-lg font-semibold text-[var(--buddychip-white)]">
                  Standard AI Responses
                </h3>
                <Badge variant="secondary" className="bg-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)]">
                  {regularResponses.length} Generated
                </Badge>
              </div>
              {regularResponses.length > 0 && (
                <div className="text-xs text-[var(--buddychip-grey-text)]">
                  💡 Tip: Use "Enhance" to improve responses with topic research and current context
                </div>
              )}
            </div>
            
            <div className="space-y-4">
              {regularResponses.map((response) => (
                <Card key={response._id} className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <Badge variant="outline" className="capitalize border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-grey-text)]">
                          {response.style}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onEnhanceResponse(response._id, response.style)}
                          className="text-green-400 hover:text-green-300 hover:bg-green-400/10"
                          title="Enhance with Topic Research"
                          disabled={isEnhancing}
                        >
                          <Sparkles className="h-4 w-4 text-white" />
                        </Button>
                        {originalUrl && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleReplyOnTwitter(response.content)}
                            className="text-[#1DA1F2] hover:text-[#1a8cd8] hover:bg-[#1DA1F2]/10"
                            title="Reply on Twitter"
                          >
                            <Reply className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleStartEdit(response._id, response.content)}
                          className="text-[var(--buddychip-accent)] hover:text-[var(--buddychip-accent)]/80 hover:bg-[var(--buddychip-accent)]/10"
                          title="Edit Response"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onCopyResponse(response.content)}
                          className="text-[var(--buddychip-white)] hover:text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]"
                          title="Copy Response"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onDeleteResponse(response._id)}
                          className="text-red-500 hover:text-red-400 hover:bg-red-500/10"
                          title="Delete Response"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Response Content */}
                    <div className="bg-[var(--buddychip-black)] border border-[var(--buddychip-grey-stroke)] rounded-lg p-4 border-l-4 border-l-[var(--buddychip-grey-stroke)]">
                      {editingResponseId === response._id ? (
                        <div className="space-y-3">
                          <Textarea
                            value={editedContent}
                            onChange={(e) => setEditedContent(e.target.value)}
                            className="min-h-[100px] resize-none bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] focus:border-[var(--buddychip-accent)]"
                            placeholder="Edit your response..."
                          />
                          <div className="flex items-center justify-between">
                            <div className="text-xs text-[var(--buddychip-grey-text)]">
                              Length: {editedContent.length} characters
                            </div>
                            <div className="flex gap-2">
                              <Button
                                size="sm"
                                onClick={() => handleSaveEdit(response._id)}
                                className="bg-green-600 hover:bg-green-600/90 text-white"
                              >
                                <Save className="h-3 w-3 mr-1" />
                                Save
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={handleCancelEdit}
                                className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
                              >
                                <X className="h-3 w-3 mr-1" />
                                Cancel
                              </Button>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <>
                          <p className="text-sm leading-relaxed text-[var(--buddychip-white)]">{response.content}</p>
                          <div className="mt-2 flex items-center justify-between text-xs text-[var(--buddychip-grey-text)]">
                            <span>Length: {response.content.length} characters</span>
                            <span>Model: {response.model || 'GPT-4'}</span>
                          </div>
                        </>
                      )}
                    </div>


                    {/* Action Buttons for Draft Status */}
                    {response.status === 'draft' && (
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={() => onApproveResponse(response._id)}
                          className="flex-1 bg-green-600 hover:bg-green-600/90 text-white border-0"
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Approve
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => onDeclineResponse(response._id)}
                          className="flex-1 border border-red-600 text-red-400 hover:bg-red-600/90 hover:text-white"
                        >
                          <XCircle className="h-4 w-4 mr-2" />
                          Decline
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
}