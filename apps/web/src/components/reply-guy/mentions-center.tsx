import { useState, useEffect } from "react";
import { useQuery, useAction } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { <PERSON><PERSON> } from "../ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "../ui/tabs";
import { EnhancedMentionCard } from "./enhanced-mention-card";
import { MonitoringSettings } from "./monitoring-settings";
import { ResponseQueue } from "./response-queue";
import { RealTimeStatus } from "./real-time-status";
import { SentimentDashboard } from "./sentiment-dashboard";
import { 
  Bell, 
  Users, 
  TrendingUp, 
  MessageSquare, 
  Settings,
  RefreshCw,
  Eye,
  Clock,
  Zap,
  BarChart3,
  ArrowUp,
  ArrowDown,
  Calendar,
  Sparkles,
  Target,
  CheckCircle
} from "lucide-react";
import { cn } from "../../lib/utils";

// --- Helper to read current hash --------------------------------------------------
const getHashTab = () => {
  if (typeof window === "undefined") return "";
  const hash = window.location.hash.replace("#", "");
  return hash;
};

interface MentionsCenterProps {
  searchQuery: string;
  selectedPriority: string;
  selectedAccount: string;
  timeRange: string;
  selectedSentiment: string;
  sortBy: string;
}

export function MentionsCenter({
  searchQuery,
  selectedPriority,
  selectedAccount,
  timeRange,
  selectedSentiment,
  sortBy
}: MentionsCenterProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<number>(Date.now());
  const [activeTab, setActiveTab] = useState<string>(() => {
    const initial = getHashTab();
    return ["mentions","sentiment","to-answer","responses","settings"].includes(initial) ? initial : "mentions";
  });
  const [refreshMessage, setRefreshMessage] = useState<string>("");
  const [refreshMetrics, setRefreshMetrics] = useState<any>(null);
  const [highlightedMentionId, setHighlightedMentionId] = useState<string | null>(null);
  const [forceRefresh, setForceRefresh] = useState(0);

  // Get current user
  const currentUser = useQuery(api.userQueries.getCurrentUser);
  const userId = currentUser?._id;

  // Using authenticated queries (JWT working correctly)
  const userStats = useQuery(api.mentions.mentionQueries.getUserMentionStats, {});
  
  // Get general mention statistics for filtering
  const mentionStats = userStats; // Use same data for now
  
  // Get unread mentions with filters (authenticated) - only show unread mentions
  const recentMentionsResult = useQuery(api.mentions.mentionQueries.getUnreadMentions, {
    limit: 50,
    // Force refresh when forceRefresh changes
    _forceRefresh: forceRefresh,
  });
  
  // Extract the data array from the paginated result (80-90% bandwidth savings!)
  const recentMentions = recentMentionsResult?.data || [];

  // Get unprocessed mentions for "To Answer" tab
  const unprocessedMentionsResult = useQuery(api.mentions.mentionQueries.getUnprocessedMentions, {
    limit: 50,
    // Force refresh when forceRefresh changes
    _forceRefresh: forceRefresh,
  });
  
  const unprocessedMentions = unprocessedMentionsResult?.data || [];

  // Get user's twitter accounts for filtering (only when user is authenticated)
  const twitterAccounts = useQuery(api.users.getUserTwitterAccounts, currentUser ? {} : "skip");

  // Action to refresh mentions
  const refreshMentionsAction = useAction(api.mentions.mentionMutations.refreshMentions);

  // Debug logging
  console.log('MentionsCenter Debug:', {
    currentUser,
    userStats,
    mentionStats,
    recentMentions: recentMentions?.length || 0
  });

  const handleRefresh = async () => {
    setIsRefreshing(true);
    setRefreshMessage("");
    setLastRefresh(Date.now());
    
    try {
      console.log('🔄 Starting OPTIMIZED mention refresh from frontend...');
      
      // Use optimized refresh with smart defaults
      const result = await refreshMentionsAction({
        enableViralDetection: true,
        priorityMode: true,
        maxConcurrency: 3,
      });
      
      console.log('✅ Optimized refresh result:', result);
      
      // Update metrics for real-time display
      if (result.metrics) {
        setRefreshMetrics(result.metrics);
      }
      
      if (result.success) {
        // Enhanced success messaging with performance metrics
        if (result.newMentions > 0) {
          const performanceInfo = result.metrics ? 
            ` (${result.metrics.totalProcessingTime}ms, ${result.metrics.apiCalls} API calls)` : 
            '';
          setRefreshMessage(
            `🚀 Found ${result.newMentions} new mention${result.newMentions !== 1 ? 's' : ''}! ` +
            `${result.message}${performanceInfo}`
          );
          
          // Show viral detection results if available
          if (result.optimizations?.viralDetectionEnabled) {
            const viralCount = result.accountResults?.reduce((sum: number, r: any) => sum + (r.viralCandidates?.length || 0), 0) || 0;
            if (viralCount > 0) {
              setTimeout(() => {
                setRefreshMessage(`🔥 ${viralCount} high-priority mentions detected and flagged for immediate attention!`);
              }, 3000);
            }
          }
        } else {
          const cacheInfo = result.metrics?.cacheHits > 0 ? 
            ` (${result.metrics.cacheHits} cache hits)` : '';
          setRefreshMessage(`✅ ${result.message || 'Refresh completed - no new mentions found'}${cacheInfo}`);
        }
        
        // Show performance metrics for power users
        if (result.metrics && result.metrics.totalProcessingTime > 0) {
          setTimeout(() => {
            setRefreshMessage(
              `📊 Performance: ${result.metrics.totalProcessingTime}ms total, ` +
              `${result.metrics.duplicatesSkipped} duplicates skipped, ` +
              `${Math.round((result.metrics.cacheHits / (result.metrics.cacheHits + result.metrics.apiCalls)) * 100)}% cache hit rate`
            );
          }, 5000);
        }
        
        setTimeout(() => setRefreshMessage(""), 12000);
      } else {
        const errorMsg = result.message || "Failed to refresh mentions";
        if (errorMsg.includes("API") || errorMsg.includes("rate limit")) {
          setRefreshMessage(`⚠️ ${errorMsg}. Please try again in a few minutes.`);
        } else if (errorMsg.includes("auth") || errorMsg.includes("credentials")) {
          setRefreshMessage(`🔐 Authentication issue: ${errorMsg}. Check your Twitter account connection.`);
        } else {
          setRefreshMessage(`❌ ${errorMsg}`);
        }
        setTimeout(() => setRefreshMessage(""), 10000);
      }
    } catch (error) {
      console.error('❌ Error refreshing mentions:', error);
      if (error instanceof Error) {
        if (error.message.includes("network") || error.message.includes("fetch")) {
          setRefreshMessage("🌐 Network error - please check your connection and try again");
        } else if (error.message.includes("timeout")) {
          setRefreshMessage("⏱️ Request timed out - the system may be busy, please try again");
        } else {
          setRefreshMessage(`❌ ${error.message}`);
        }
      } else {
        setRefreshMessage("❌ Unexpected error occurred while refreshing mentions");
      }
      setTimeout(() => setRefreshMessage(""), 8000);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setLastRefresh(Date.now());
    }, 30000);
    return () => clearInterval(interval);
  }, []);

  // Filter and sort mentions (updated for optimized LightweightMention fields)
  const filteredMentions = recentMentions?.filter((mention: any) => {
    const matchesSearch = searchQuery === "" || 
      mention.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      mention.authorName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      mention.authorHandle.toLowerCase().includes(searchQuery.toLowerCase());
    
    // Note: Sentiment analysis not available in LightweightMention (bandwidth optimization)
    // For sentiment filtering, we'd need to use the full mention query
    const matchesSentiment = selectedSentiment === "all"; // Always true for now
    
    return matchesSearch && matchesSentiment;
  }).sort((a: any, b: any) => {
    switch (sortBy) {
      case "newest":
        return b.createdAt - a.createdAt;
      case "oldest":
        return a.createdAt - b.createdAt;
      case "priority":
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return (priorityOrder[b.priority as keyof typeof priorityOrder] || 0) - 
               (priorityOrder[a.priority as keyof typeof priorityOrder] || 0);
      case "engagement":
        return (b.engagement.likes + b.engagement.retweets + b.engagement.replies) -
               (a.engagement.likes + a.engagement.retweets + a.engagement.replies);
      case "sentiment":
        // Note: Sentiment data not available in LightweightMention (bandwidth optimization)
        // Fall back to priority-based sorting
        const priorityOrderSentiment = { high: 3, medium: 2, low: 1 };
        return (priorityOrderSentiment[b.priority as keyof typeof priorityOrderSentiment] || 0) - 
               (priorityOrderSentiment[a.priority as keyof typeof priorityOrderSentiment] || 0);
      default:
        return b.createdAt - a.createdAt;
    }
  });


  // Sync tab changes <-> hash ------------------------------------------------------
  useEffect(() => {
    const handleHashChange = () => {
      const hash = getHashTab();
      
      // Check if hash is a tab name
      if (["mentions","sentiment","to-answer","responses","settings"].includes(hash)) {
        if (hash !== activeTab) {
          setActiveTab(hash);
        }
      } else if (hash && hash.length > 10) {
        // Assume it's a mention ID if it's long enough
        setActiveTab("mentions"); // Switch to mentions tab
        setHighlightedMentionId(hash);
        
        // Scroll to mention after a short delay to ensure DOM is updated
        setTimeout(() => {
          const mentionElement = document.getElementById(`mention-${hash}`);
          if (mentionElement) {
            mentionElement.scrollIntoView({ 
              behavior: "smooth", 
              block: "center" 
            });
          }
        }, 300);
      }
    };

    window.addEventListener("hashchange", handleHashChange);
    
    // Handle initial hash on mount
    handleHashChange();
    
    return () => window.removeEventListener("hashchange", handleHashChange);
  }, [activeTab]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const current = getHashTab();
      if (activeTab !== current && ["mentions","sentiment","to-answer","responses","settings"].includes(activeTab)) {
        window.location.hash = activeTab;
      }
    }
  }, [activeTab]);

  // Clear highlighted mention after 5 seconds
  useEffect(() => {
    if (highlightedMentionId) {
      const timer = setTimeout(() => {
        setHighlightedMentionId(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [highlightedMentionId]);

  // Handle response generation callback
  const handleResponseGenerated = () => {
    // Force a refresh of mention queries to show new responses
    setForceRefresh(prev => prev + 1);
    console.log('🚀 Response generated, refreshing mention data...');
  };

  return (
    <div className="space-y-6">
      {/* Animated Background Elements */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/5 rounded-full blur-3xl animate-pulse" />
        <div className="absolute top-40 right-20 w-48 h-48 bg-purple-500/3 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute bottom-20 left-1/3 w-40 h-40 bg-cyan-500/4 rounded-full blur-3xl animate-pulse delay-2000" />
      </div>

      {/* Refresh Message Display */}
      {refreshMessage && (
        <div className="relative z-10">
          <Card className={cn(
            "bg-gradient-to-r border backdrop-blur-sm",
            refreshMessage.includes("❌") || refreshMessage.includes("⚠️") || refreshMessage.includes("🔐") || refreshMessage.includes("🌐") || refreshMessage.includes("⏱️")
              ? "from-red-500/10 to-orange-500/10 border-red-500/30"
              : refreshMessage.includes("✅") && refreshMessage.includes("Found")
                ? "from-green-500/10 to-emerald-500/10 border-green-500/30"
                : "from-blue-500/10 to-cyan-500/10 border-blue-500/30"
          )}>
            <CardContent className="py-3">
              <div className="flex items-center gap-3">
                <div className={cn(
                  "w-2 h-2 rounded-full animate-pulse",
                  refreshMessage.includes("❌") || refreshMessage.includes("⚠️") || refreshMessage.includes("🔐") || refreshMessage.includes("🌐") || refreshMessage.includes("⏱️")
                    ? "bg-red-400"
                    : refreshMessage.includes("✅") && refreshMessage.includes("Found")
                      ? "bg-green-400"
                      : "bg-blue-400"
                )} />
                <span className={cn(
                  "text-sm font-medium",
                  refreshMessage.includes("❌") || refreshMessage.includes("⚠️") || refreshMessage.includes("🔐") || refreshMessage.includes("🌐") || refreshMessage.includes("⏱️")
                    ? "text-red-300"
                    : refreshMessage.includes("✅") && refreshMessage.includes("Found")
                      ? "text-green-300"
                      : "text-blue-300"
                )}>
                  {refreshMessage}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 relative z-10">
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all duration-300 hover:border-[#316FE3]/50 group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#F5F7FA] group-hover:text-[#316FE3] transition-colors">Unread Mentions</CardTitle>
            <div className="relative">
              <Bell className="h-4 w-4 text-[#316FE3] group-hover:text-blue-400 transition-colors" />
              {(mentionStats?.unread || 0) > 0 && (
                <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full animate-pulse" />
              )}
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#F5F7FA] group-hover:text-[#316FE3] transition-colors">
              {userStats === undefined ? (
                <div className="animate-pulse bg-[#316FE3]/20 h-8 w-12 rounded"></div>
              ) : (
                userStats?.unreadCount || 0
              )}
            </div>
            <div className="flex items-center gap-1 text-xs text-[#6E7A8C]">
              <span>Awaiting your attention</span>
              {(userStats?.unreadCount || 0) > 0 && (
                <div className="flex items-center gap-1 text-red-400">
                  <div className="w-1 h-1 bg-red-400 rounded-full animate-pulse" />
                  <span>New</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all duration-300 hover:border-orange-500/50 group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#F5F7FA] group-hover:text-orange-400 transition-colors">High Priority</CardTitle>
            <TrendingUp className="h-4 w-4 text-orange-500 group-hover:text-orange-400 transition-colors" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#F5F7FA] group-hover:text-orange-400 transition-colors">
              {userStats === undefined ? (
                <div className="animate-pulse bg-orange-500/20 h-8 w-12 rounded"></div>
              ) : (
                userStats?.highPriorityCount || 0
              )}
            </div>
            <p className="text-xs text-[#6E7A8C]">
              Verified or high-engagement authors
            </p>
            {(userStats?.highPriorityCount || 0) > 0 && (
              <div className="mt-2 flex items-center gap-1 text-xs text-orange-400">
                <Zap className="h-3 w-3" />
                <span>Urgent attention needed</span>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all duration-300 hover:border-green-500/50 group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#F5F7FA] group-hover:text-green-400 transition-colors">Processed</CardTitle>
            <MessageSquare className="h-4 w-4 text-green-500 group-hover:text-green-400 transition-colors" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#F5F7FA] group-hover:text-green-400 transition-colors">
              {userStats === undefined || mentionStats === undefined ? (
                <div className="animate-pulse bg-green-500/20 h-8 w-12 rounded"></div>
              ) : (
                Math.max(0, (userStats?.totalMentions || 0) - (mentionStats?.unprocessed || 0))
              )}
            </div>
            <p className="text-xs text-[#6E7A8C]">
              AI analysis completed
            </p>
            <div className="mt-2">
              <div className="text-xs text-green-400 flex items-center gap-1">
                <div className="w-1 h-1 bg-green-400 rounded-full" />
                <span>{Math.round(Math.max(0, (userStats?.totalMentions || 0) - (mentionStats?.unprocessed || 0)) / Math.max(userStats?.totalMentions || 1, 1) * 100)}% completion rate</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all duration-300 hover:border-purple-500/50 group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#F5F7FA] group-hover:text-purple-400 transition-colors">Responses Ready</CardTitle>
            <Sparkles className="h-4 w-4 text-purple-500 group-hover:text-purple-400 transition-colors" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#F5F7FA] group-hover:text-purple-400 transition-colors">
              {userStats === undefined ? (
                <div className="animate-pulse bg-purple-500/20 h-8 w-12 rounded"></div>
              ) : (
                userStats?.responseOpportunities || 0
              )}
            </div>
            <p className="text-xs text-[#6E7A8C]">
              Ready for AI response generation
            </p>
            {(userStats?.responseOpportunities || 0) > 0 && (
              <div className="mt-2 flex items-center gap-1 text-xs text-purple-400">
                <Target className="h-3 w-3" />
                <span>Opportunities available</span>
              </div>
            )}
          </CardContent>
        </Card>
      </div>


      {/* Real-Time Status Panel */}
      <RealTimeStatus 
        isRefreshing={isRefreshing}
        lastRefreshTime={lastRefresh}
        refreshMetrics={refreshMetrics}
      />

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5 bg-[var(--buddychip-black)] border border-[var(--buddychip-grey-stroke)] p-1 rounded-lg">
          <TabsTrigger 
            value="mentions" 
            className="text-[var(--buddychip-grey-text)] data-[state=active]:text-[var(--buddychip-white)] data-[state=active]:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/50 transition-all duration-200 rounded-md"
          >
            Recent Mentions
          </TabsTrigger>
          <TabsTrigger 
            value="sentiment"
            className="text-[var(--buddychip-grey-text)] data-[state=active]:text-[var(--buddychip-white)] data-[state=active]:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/50 transition-all duration-200 rounded-md"
          >
            Sentiment Analytics
          </TabsTrigger>
          <TabsTrigger 
            value="to-answer"
            className="text-[var(--buddychip-grey-text)] data-[state=active]:text-[var(--buddychip-white)] data-[state=active]:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/50 transition-all duration-200 rounded-md"
          >
            To Answer ({unprocessedMentions.length})
          </TabsTrigger>
          <TabsTrigger 
            value="responses"
            className="text-[var(--buddychip-grey-text)] data-[state=active]:text-[var(--buddychip-white)] data-[state=active]:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/50 transition-all duration-200 rounded-md"
          >
            Response Queue
          </TabsTrigger>
          <TabsTrigger 
            value="settings"
            className="text-[var(--buddychip-grey-text)] data-[state=active]:text-[var(--buddychip-white)] data-[state=active]:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]/50 transition-all duration-200 rounded-md"
          >
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="mentions" className="space-y-4 relative z-10">
          {/* Loading State */}
          {recentMentions === undefined && currentUser && twitterAccounts && twitterAccounts.length > 0 && (
            <Card className="bg-[#000000]/70 border-[#202631] shadow-sm backdrop-blur-sm">
              <CardContent className="py-8">
                <div className="text-center">
                  <div className="animate-spin h-8 w-8 border-2 border-[#316FE3] border-t-transparent rounded-full mx-auto mb-4"></div>
                  <h3 className="text-lg font-semibold mb-2 text-[#F5F7FA]">Loading mentions...</h3>
                  <p className="text-[#6E7A8C]">
                    Fetching your latest mentions and responses from the database
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Results Summary */}
          {filteredMentions && filteredMentions.length > 0 && (
            <div className="flex items-center justify-between text-sm text-[#6E7A8C] bg-[#000000]/40 px-4 py-2 rounded-lg border border-[#202631] shadow-sm backdrop-blur-sm">
              <span className="text-[#F5F7FA]">
                Showing {filteredMentions.length} of {recentMentions?.length || 0} mentions
                {searchQuery && ` matching "${searchQuery}"`}
              </span>
              <div className="flex items-center gap-4">
                <Badge variant="outline" className="text-xs border-[#202631] text-[#6E7A8C]">
                  {filteredMentions.filter((m: any) => m.priority === 'high').length} high priority
                </Badge>
                <Badge variant="outline" className="text-xs border-[#202631] text-[#6E7A8C]">
                  {filteredMentions.filter((m: any) => !m.isNotificationSent).length} unread
                </Badge>
              </div>
            </div>
          )}
          
          {!currentUser ? (
            <Card className="bg-[#000000]/70 border-[#202631] shadow-sm backdrop-blur-sm">
              <CardContent className="py-12">
                <div className="text-center">
                  <Bell className="h-12 w-12 text-[#6E7A8C] mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2 text-[#F5F7FA]">Authentication required</h3>
                  <p className="text-[#6E7A8C]">
                    Please log in to view your mentions and manage responses.
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : !twitterAccounts || twitterAccounts.length === 0 ? (
            <Card className="bg-[#000000]/70 border-[#202631] shadow-sm backdrop-blur-sm">
              <CardContent className="py-12">
                <div className="text-center">
                  <Bell className="h-12 w-12 text-[#6E7A8C] mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2 text-[#F5F7FA]">No Twitter accounts connected</h3>
                  <p className="text-[#6E7A8C] mb-4">
                    Connect your Twitter accounts to start monitoring mentions and generating AI responses.
                  </p>
                  <Button 
                    variant="ghost" 
                    className="border border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)]"
                    onClick={() => window.location.href = '/dashboard'}
                  >
                    Go to Dashboard
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : !filteredMentions || filteredMentions.length === 0 ? (
            <Card className="bg-[#000000]/70 border-[#202631] shadow-sm backdrop-blur-sm">
              <CardContent className="py-12">
                <div className="text-center">
                  <Bell className="h-12 w-12 text-[#6E7A8C] mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2 text-[#F5F7FA]">No mentions found</h3>
                  <p className="text-[#6E7A8C] mb-4">
                    {recentMentions === undefined 
                      ? "Loading mentions..." 
                      : searchQuery 
                        ? `No mentions found matching "${searchQuery}". Try adjusting your search or filters.`
                        : selectedPriority !== "all" || selectedAccount !== "all" || timeRange !== "24h"
                          ? "No mentions found for the selected filters. Try broadening your search criteria."
                          : "No mentions found yet. Make sure monitoring is enabled for your accounts and check back later."
                    }
                  </p>
                  {!searchQuery && selectedPriority === "all" && selectedAccount === "all" && timeRange === "24h" && (
                    <div className="space-y-3">
                      <Button 
                        variant="ghost" 
                        className="border border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)]"
                        onClick={handleRefresh}
                        disabled={isRefreshing}
                      >
                        <RefreshCw className={cn("h-4 w-4 mr-2", isRefreshing && "animate-spin")} />
                        {isRefreshing ? "Checking for mentions..." : "Check for new mentions"}
                      </Button>
                      <p className="text-xs text-[#6E7A8C]">
                        Tip: Enable monitoring in settings and ensure your accounts are active
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredMentions.map((mention: any, index: number) => (
                <div 
                  key={mention._id}
                  id={`mention-${mention._id}`}
                  className={cn(
                    "animate-in slide-in-from-bottom-4 duration-300 transition-all",
                    highlightedMentionId === mention._id && 
                    "ring-2 ring-blue-500/50 rounded-lg bg-blue-500/5"
                  )}
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <EnhancedMentionCard 
                    mention={mention} 
                    isHighlighted={highlightedMentionId === mention._id}
                    onResponseGenerated={handleResponseGenerated}
                  />
                </div>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="sentiment" className="space-y-4">
          <SentimentDashboard
            accountId={selectedAccount !== "all" ? selectedAccount : undefined}
            timeRange={timeRange as "24h" | "7d" | "30d"}
          />
        </TabsContent>

        <TabsContent value="to-answer" className="space-y-4 relative z-10">
          {/* Results Summary for To Answer */}
          {unprocessedMentions && unprocessedMentions.length > 0 && (
            <div className="flex items-center justify-between text-sm text-[#6E7A8C] bg-[#000000]/40 px-4 py-2 rounded-lg border border-[#202631] shadow-sm backdrop-blur-sm">
              <span className="text-[#F5F7FA]">
                {unprocessedMentions.length} mention{unprocessedMentions.length !== 1 ? 's' : ''} waiting for response
              </span>
              <div className="flex items-center gap-4">
                <Badge variant="outline" className="text-xs border-red-500/30 text-red-300 bg-red-500/10">
                  {unprocessedMentions.filter((m: any) => m.priority === 'high').length} high priority
                </Badge>
                <Badge variant="outline" className="text-xs border-yellow-500/30 text-yellow-300 bg-yellow-500/10">
                  {unprocessedMentions.filter((m: any) => m.priority === 'medium').length} medium priority
                </Badge>
              </div>
            </div>
          )}

          {!currentUser ? (
            <Card className="bg-[#000000]/70 border-[#202631] shadow-sm backdrop-blur-sm">
              <CardContent className="py-12">
                <div className="text-center">
                  <MessageSquare className="h-12 w-12 text-[#6E7A8C] mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2 text-[#F5F7FA]">Authentication required</h3>
                  <p className="text-[#6E7A8C]">
                    Please log in to view mentions that need responses.
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : !twitterAccounts || twitterAccounts.length === 0 ? (
            <Card className="bg-[#000000]/70 border-[#202631] shadow-sm backdrop-blur-sm">
              <CardContent className="py-12">
                <div className="text-center">
                  <MessageSquare className="h-12 w-12 text-[#6E7A8C] mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2 text-[#F5F7FA]">No Twitter accounts connected</h3>
                  <p className="text-[#6E7A8C] mb-4">
                    Connect your Twitter accounts to start monitoring mentions that need responses.
                  </p>
                  <Button 
                    variant="ghost" 
                    className="border border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)]"
                    onClick={() => window.location.href = '/dashboard'}
                  >
                    Go to Dashboard
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : !unprocessedMentions || unprocessedMentions.length === 0 ? (
            <Card className="bg-[#000000]/70 border-[#202631] shadow-sm backdrop-blur-sm">
              <CardContent className="py-12">
                <div className="text-center">
                  <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2 text-[#F5F7FA]">All caught up!</h3>
                  <p className="text-[#6E7A8C] mb-4">
                    {unprocessedMentions === undefined 
                      ? "Loading mentions that need responses..." 
                      : "Great job! You've responded to all your mentions. Check back later for new ones."
                    }
                  </p>
                  {unprocessedMentions !== undefined && (
                    <Button 
                      variant="ghost" 
                      className="border border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)]"
                      onClick={handleRefresh}
                      disabled={isRefreshing}
                    >
                      <RefreshCw className={cn("h-4 w-4 mr-2", isRefreshing && "animate-spin")} />
                      {isRefreshing ? "Checking..." : "Check for new mentions"}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {unprocessedMentions.map((mention: any, index: number) => (
                <div 
                  key={mention._id}
                  id={`mention-${mention._id}`}
                  className={cn(
                    "animate-in slide-in-from-bottom-4 duration-300 transition-all",
                    highlightedMentionId === mention._id && 
                    "ring-2 ring-blue-500/50 rounded-lg bg-blue-500/5"
                  )}
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <EnhancedMentionCard 
                    mention={mention} 
                    isHighlighted={highlightedMentionId === mention._id}
                    onResponseGenerated={handleResponseGenerated}
                  />
                </div>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="responses" className="space-y-4">
          {userId ? (
            <ResponseQueue userId={userId} />
          ) : (
            <Card className="bg-[#000000]/70 border-[#202631] shadow-sm backdrop-blur-sm">
              <CardContent className="py-12">
                <div className="text-center">
                  <MessageSquare className="h-12 w-12 text-[#6E7A8C] mx-auto mb-4" />
                  <p className="text-[#6E7A8C]">
                    Please log in to view response queue
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="settings" className="space-y-4" id="mentions-settings-section">
          <MonitoringSettings />
        </TabsContent>
      </Tabs>
    </div>
  );
}