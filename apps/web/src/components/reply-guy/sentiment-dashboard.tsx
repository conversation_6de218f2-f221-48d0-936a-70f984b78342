import { useState } from "react";
import { useQuery, useAction } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { But<PERSON> } from "../ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";
import { Progress } from "../ui/progress";
import { SentimentBar, SentimentIndicator } from "../ui/sentiment-bar";
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  PieChart, 
  Activity,
  Target,
  Zap,
  Eye,
  MessageSquare,
  Heart,
  AlertTriangle,
  CheckCircle,
  Clock,
  Sparkles
} from "lucide-react";
import { cn } from "../../lib/utils";
import { toast } from "sonner";

interface SentimentDashboardProps {
  accountId?: string;
  timeRange?: "24h" | "7d" | "30d";
  className?: string;
}

export function SentimentDashboard({ 
  accountId, 
  timeRange = "7d", 
  className 
}: SentimentDashboardProps) {
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);
  const [isProcessing, setIsProcessing] = useState(false);

  // Action to start sentiment analysis
  const startSentimentAnalysis = useAction(api.ai.batchSentimentProcessing.startSentimentAnalysisForUnprocessed);

  // Get overall sentiment stats if no specific account
  const overallStats = useQuery(
    !accountId ? api.mentions.sentimentQueries.getOverallSentimentStats : "skip",
    { timeRange: selectedTimeRange }
  );

  // Get account-specific sentiment analytics if account provided
  const accountStats = useQuery(
    accountId ? api.mentions.sentimentQueries.getAccountSentimentAnalytics : "skip",
    accountId ? { accountId, timeRange: selectedTimeRange } : "skip"
  );

  const stats = accountStats || overallStats;

  const handleStartSentimentAnalysis = async () => {
    setIsProcessing(true);
    try {
      toast.info("🔍 Starting sentiment analysis for unprocessed mentions...");
      
      const result = await startSentimentAnalysis({
        maxBatchSize: 50,
        accountId: accountId,
      });

      if (result.success) {
        toast.success(`✅ Sentiment analysis completed! Processed ${result.processed} mentions.`);
      } else {
        toast.error(`❌ ${result.message}`);
      }
    } catch (error) {
      console.error('Failed to start sentiment analysis:', error);
      toast.error("Failed to start sentiment analysis. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  if (!stats) {
    return (
      <Card className={cn("bg-[#000000]/70 border-[#202631] backdrop-blur-sm", className)}>
        <CardContent className="py-8">
          <div className="text-center">
            <div className="animate-spin h-8 w-8 border-2 border-[#316FE3] border-t-transparent rounded-full mx-auto mb-4"></div>
            <h3 className="text-lg font-semibold mb-2 text-[#F5F7FA]">Loading sentiment analytics...</h3>
          </div>
        </CardContent>
      </Card>
    );
  }

  const sentimentPercentages = {
    bullish: stats.analyzedMentions > 0 ? Math.round((stats.sentimentBreakdown.bullish / stats.analyzedMentions) * 100) : 0,
    bearish: stats.analyzedMentions > 0 ? Math.round((stats.sentimentBreakdown.bearish / stats.analyzedMentions) * 100) : 0,
    neutral: stats.analyzedMentions > 0 ? Math.round((stats.sentimentBreakdown.neutral / stats.analyzedMentions) * 100) : 0,
  };

  const primarySentiment = 
    sentimentPercentages.bullish > sentimentPercentages.bearish && sentimentPercentages.bullish > sentimentPercentages.neutral ? "bullish" :
    sentimentPercentages.bearish > sentimentPercentages.bullish && sentimentPercentages.bearish > sentimentPercentages.neutral ? "bearish" :
    "neutral";

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header with time range selector */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-[#F5F7FA] flex items-center gap-2">
            <BarChart3 className="h-6 w-6 text-[#316FE3]" />
            Sentiment Analytics
          </h2>
          <p className="text-[#6E7A8C]">
            {accountId ? "Account-specific" : "Overall"} sentiment analysis for the last {selectedTimeRange}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            onClick={handleStartSentimentAnalysis}
            disabled={isProcessing}
            className="bg-purple-600 hover:bg-purple-700 text-white border-purple-600"
          >
            {isProcessing ? (
              <>
                <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                Processing...
              </>
            ) : (
              <>
                <BarChart3 className="h-4 w-4 mr-2 text-white" />
                Start Sentiment Analysis
              </>
            )}
          </Button>
          <Tabs value={selectedTimeRange} onValueChange={(value) => setSelectedTimeRange(value as typeof selectedTimeRange)}>
            <TabsList className="bg-[#000000]/40 border border-[#202631]">
              <TabsTrigger value="24h" className="text-[#6E7A8C] data-[state=active]:text-[#F5F7FA]">24h</TabsTrigger>
              <TabsTrigger value="7d" className="text-[#6E7A8C] data-[state=active]:text-[#F5F7FA]">7d</TabsTrigger>
              <TabsTrigger value="30d" className="text-[#6E7A8C] data-[state=active]:text-[#F5F7FA]">30d</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Total Mentions */}
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#F5F7FA]">Total Mentions</CardTitle>
            <MessageSquare className="h-4 w-4 text-[#316FE3]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#F5F7FA]">{stats.totalMentions}</div>
            <p className="text-xs text-[#6E7A8C]">
              {stats.analyzedMentions} analyzed ({Math.round((stats.analyzedMentions / Math.max(stats.totalMentions, 1)) * 100)}%)
            </p>
          </CardContent>
        </Card>

        {/* Average Sentiment Score */}
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#F5F7FA]">Average Score</CardTitle>
            <Target className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#F5F7FA]">{stats.averageScore}/100</div>
            <div className="mt-2">
              <SentimentIndicator
                sentiment={primarySentiment}
                score={stats.averageScore}
                size="sm"
                showScore={false}
              />
            </div>
          </CardContent>
        </Card>

        {/* Primary Sentiment */}
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#F5F7FA]">Dominant Sentiment</CardTitle>
            {primarySentiment === "bullish" ? <TrendingUp className="h-4 w-4 text-green-500" /> :
             primarySentiment === "bearish" ? <TrendingDown className="h-4 w-4 text-red-500" /> :
             <Activity className="h-4 w-4 text-gray-500" />}
          </CardHeader>
          <CardContent>
            <div className={cn(
              "text-2xl font-bold capitalize",
              primarySentiment === "bullish" ? "text-green-400" :
              primarySentiment === "bearish" ? "text-red-400" :
              "text-gray-400"
            )}>
              {primarySentiment}
            </div>
            <p className="text-xs text-[#6E7A8C]">
              {sentimentPercentages[primarySentiment]}% of analyzed mentions
            </p>
          </CardContent>
        </Card>

        {/* Analysis Quality */}
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm hover:bg-[#000000]/90 transition-all group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[#F5F7FA]">Confidence</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#F5F7FA]">
              {Math.round((stats.confidence || 0.7) * 100)}%
            </div>
            <p className="text-xs text-[#6E7A8C]">
              Average AI confidence
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sentiment Breakdown */}
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
              <PieChart className="h-5 w-5 text-[#316FE3]" />
              Sentiment Distribution
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Bullish */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium text-[#F5F7FA]">Bullish</span>
                </div>
                <span className="text-sm text-green-400 font-bold">
                  {stats.sentimentBreakdown.bullish} ({sentimentPercentages.bullish}%)
                </span>
              </div>
              <Progress 
                value={sentimentPercentages.bullish} 
                className="h-2 bg-gray-800"
                style={{ 
                  background: 'linear-gradient(to right, #22c55e 0%, #22c55e 100%)'
                }}
              />
            </div>

            {/* Neutral */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
                  <span className="text-sm font-medium text-[#F5F7FA]">Neutral</span>
                </div>
                <span className="text-sm text-gray-400 font-bold">
                  {stats.sentimentBreakdown.neutral} ({sentimentPercentages.neutral}%)
                </span>
              </div>
              <Progress 
                value={sentimentPercentages.neutral} 
                className="h-2 bg-gray-800"
              />
            </div>

            {/* Bearish */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-sm font-medium text-[#F5F7FA]">Bearish</span>
                </div>
                <span className="text-sm text-red-400 font-bold">
                  {stats.sentimentBreakdown.bearish} ({sentimentPercentages.bearish}%)
                </span>
              </div>
              <Progress 
                value={sentimentPercentages.bearish} 
                className="h-2 bg-gray-800"
              />
            </div>
          </CardContent>
        </Card>

        {/* Top Keywords */}
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
              <Sparkles className="h-5 w-5 text-purple-500" />
              Top Sentiment Keywords
            </CardTitle>
          </CardHeader>
          <CardContent>
            {stats.topKeywords && stats.topKeywords.length > 0 ? (
              <div className="space-y-3">
                {stats.topKeywords.slice(0, 8).map((item: any, idx: number) => (
                  <div key={idx} className="flex items-center justify-between">
                    <span className="text-sm text-[#F5F7FA] bg-[#000000]/60 px-3 py-1 rounded-full border border-[#202631]">
                      {item.keyword}
                    </span>
                    <div className="flex items-center gap-2">
                      <div className="w-16 h-2 bg-gray-800 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-gradient-to-r from-[#316FE3] to-purple-500"
                          style={{ width: `${Math.min(100, (item.count / (stats.topKeywords[0]?.count || 1)) * 100)}%` }}
                        />
                      </div>
                      <span className="text-xs text-[#6E7A8C] w-8 text-right">{item.count}</span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Sparkles className="h-12 w-12 text-[#6E7A8C] mx-auto mb-2" />
                <p className="text-[#6E7A8C]">No keywords analyzed yet</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Sentiment Trend Chart */}
      {stats.sentimentTrend && stats.sentimentTrend.length > 0 && (
        <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-[#F5F7FA]">
              <Activity className="h-5 w-5 text-[#316FE3]" />
              Sentiment Trend
            </CardTitle>
            <CardDescription className="text-[#6E7A8C]">
              Average sentiment score over time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-end justify-between gap-2">
              {stats.sentimentTrend.map((point: any, idx: number) => {
                const height = Math.max(8, (point.averageScore / 100) * 100);
                const color = point.averageScore >= 60 ? "bg-green-500" : 
                            point.averageScore <= 40 ? "bg-red-500" : "bg-gray-500";
                
                return (
                  <div key={idx} className="flex flex-col items-center flex-1 max-w-8">
                    <div className="text-xs text-[#6E7A8C] mb-1">{point.count}</div>
                    <div 
                      className={cn("w-full rounded-t transition-all hover:opacity-80", color)}
                      style={{ height: `${height}%` }}
                      title={`Score: ${point.averageScore}, Count: ${point.count}`}
                    />
                    <div className="text-xs text-[#6E7A8C] mt-1 transform -rotate-45 origin-bottom-left">
                      {new Date(point.timestamp).toLocaleDateString('en-US', { 
                        month: 'short', 
                        day: 'numeric' 
                      })}
                    </div>
                  </div>
                );
              })}
            </div>
            <div className="flex justify-between text-xs text-[#6E7A8C] mt-4 border-t border-[#202631] pt-4">
              <span>Very Bearish (0-25)</span>
              <span>Neutral (40-60)</span>
              <span>Very Bullish (75-100)</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Most Extreme Mentions */}
      {(stats.mostBullishMention || stats.mostBearishMention) && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Most Bullish */}
          {stats.mostBullishMention && (
            <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm border-l-4 border-l-green-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-400 text-lg">
                  <TrendingUp className="h-5 w-5" />
                  Most Bullish Mention
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-[#6E7A8C]">@{stats.mostBullishMention.author}</span>
                    <SentimentIndicator
                      sentiment="bullish"
                      score={stats.mostBullishMention.score}
                      size="sm"
                    />
                  </div>
                  <p className="text-sm text-[#F5F7FA] bg-[#000000]/60 p-3 rounded border border-[#202631]">
                    "{stats.mostBullishMention.content}"
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Most Bearish */}
          {stats.mostBearishMention && (
            <Card className="bg-[#000000]/70 border-[#202631] backdrop-blur-sm border-l-4 border-l-red-500">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-400 text-lg">
                  <TrendingDown className="h-5 w-5" />
                  Most Bearish Mention
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-[#6E7A8C]">@{stats.mostBearishMention.author}</span>
                    <SentimentIndicator
                      sentiment="bearish"
                      score={stats.mostBearishMention.score}
                      size="sm"
                    />
                  </div>
                  <p className="text-sm text-[#F5F7FA] bg-[#000000]/60 p-3 rounded border border-[#202631]">
                    "{stats.mostBearishMention.content}"
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}