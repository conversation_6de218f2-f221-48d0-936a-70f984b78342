import { useState } from "react";
import { useMutation, useQuery, useAction } from "convex/react";
import { api } from "@BuddyChipAI/backend";
import { Card, CardContent, CardHeader } from "../ui/card";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { Separator } from "../ui/separator";
import { EnhancedResponseDisplay } from "./enhanced-response-display";
import { ContextInsightsPanel } from "./context-insights-panel";
import { ResponseGenerator } from "./response-generator";
import { SentimentBar, SentimentIndicator } from "../ui/sentiment-bar";
import { 
  MessageSquare, 
  Heart, 
  Repeat2, 
  Eye, 
  Calendar,
  ExternalLink,
  Check,
  ChevronDown,
  ChevronUp,
  Sparkles,
  Brain,
  Loader2,
  Search,
  Zap,
  TrendingUp,
  Clock,
  Users
} from "lucide-react";
import { toast } from "sonner";
import { cn } from "../../lib/utils";

interface EnhancedMentionCardProps {
  mention: any; // Type from Convex schema
  isHighlighted?: boolean;
  onResponseGenerated?: () => void;
}

export function EnhancedMentionCard({ mention, isHighlighted = false, onResponseGenerated }: EnhancedMentionCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [isImprovingContext, setIsImprovingContext] = useState(false);
  const [improvedContext, setImprovedContext] = useState<any>(null);
  const [showContextInsights, setShowContextInsights] = useState(false);

  // Get current user
  const currentUser = useQuery(api.userQueries.getCurrentUser);
  
  // Get responses for this mention
  const mentionResponses = useQuery(api.responseQueries.getResponsesForTarget, {
    targetType: "mention",
    targetId: mention._id,
  });

  // Enhanced responses (responses that have been enhanced with AI research)
  const enhancedResponses = mentionResponses?.filter(r => r.isEnhanced || r.model?.includes("Enhanced")) || [];
  const hasEnhancedResponses = enhancedResponses.length > 0;

  // Mutations and Actions
  const updateResponseStatus = useMutation(api.responseMutations.updateResponseStatus);
  const editResponse = useMutation(api.responseMutations.editResponse);
  const deleteResponse = useMutation(api.responseMutations.deleteResponse);
  const markNotificationSent = useMutation(api.mentions.mentionMutations.markNotificationSent);
  const improveResponseContext = useAction(api.ai.simpleContextImprovement.improveResponseContext);
  const generateWithImprovedContext = useAction(api.ai.simpleContextImprovement.generateResponseWithImprovedContext);

  const formatTimeAgo = (timestamp: number) => {
    const diff = Date.now() - timestamp;
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    return 'Just now';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };

  const handleToggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const handleMarkAsRead = async () => {
    try {
      await markNotificationSent({ mentionId: mention._id });
      toast.success("Marked as read");
    } catch (error) {
      toast.error("Failed to mark as read");
    }
  };

  const handleCopyResponse = (content: string) => {
    navigator.clipboard.writeText(content);
    toast.success("Response copied to clipboard");
  };

  const handleEditResponse = async (responseId: string, content: string) => {
    try {
      await editResponse({
        responseId: responseId,
        content: content,
        notes: "User edited response",
      });
      toast.success("Response updated successfully!");
    } catch (error) {
      console.error("Failed to update response:", error);
      toast.error("Failed to update response");
    }
  };

  const handleApproveResponse = async (responseId: string) => {
    try {
      await updateResponseStatus({
        responseId: responseId as any,
        status: "approved",
      });
      toast.success("Response approved!");
    } catch (error) {
      toast.error("Failed to approve response");
    }
  };

  const handleDeclineResponse = async (responseId: string) => {
    try {
      await updateResponseStatus({
        responseId: responseId as any,
        status: "declined",
      });
      toast.success("Response declined");
    } catch (error) {
      toast.error("Failed to decline response");
    }
  };

  const handleDeleteResponse = async (responseId: string) => {
    try {
      await deleteResponse({
        responseId: responseId,
      });
      toast.success("Response deleted");
    } catch (error) {
      toast.error("Failed to delete response");
    }
  };

  const handleImproveContext = async () => {
    if (!currentUser) {
      toast.error("User not authenticated");
      return;
    }

    setIsImprovingContext(true);
    try {
      toast.info("🔍 Researching topic with xAI and Perplexity...");
      
      const currentResponseData = mentionResponses?.map(r => ({
        content: r.content,
        style: r.style,
        confidence: r.confidence,
      })) || [];

      const result = await improveResponseContext({
        originalContent: mention.mentionContent || mention.content,
        responseType: "mention",
        authorInfo: {
          handle: mention.mentionAuthorHandle || mention.authorHandle,
          displayName: mention.mentionAuthor || mention.authorName,
          isVerified: mention.authorIsVerified,
          followerCount: mention.authorFollowerCount,
        },
        userContext: {
          expertise: ["social media", "content creation"],
          interests: ["engagement", "trending topics"],
          writingStyle: "professional",
          brand: "personal brand",
        },
        currentResponses: currentResponseData,
        researchFocus: ["current_events", "topic_background", "recent_developments", "factual_context"],
        maxSteps: 5,
      });

      if (result.success) {
        setImprovedContext(result.improvedContext);
        setShowContextInsights(true);
        toast.success(`📊 Topic research complete! Found ${result.summary.enhancementsGenerated} insights from ${result.summary.researchSourcesUsed} sources`);
      } else {
        throw new Error(result.error || "Topic research failed");
      }
    } catch (error) {
      console.error("Topic research failed:", error);
      toast.error("Failed to research topic. Please try again.");
    } finally {
      setIsImprovingContext(false);
    }
  };

  const handleEnhanceResponse = async (responseId: string, style: string) => {
    setIsEnhancing(true);
    
    try {
      let contextToUse = improvedContext;
      
      // If no context available, research topic first
      if (!contextToUse) {
        toast.info("🔍 Researching topic to add context...");
        
        const currentResponseData = mentionResponses?.map(r => ({
          content: r.content,
          style: r.style,
          confidence: r.confidence,
        })) || [];

        const contextResult = await improveResponseContext({
          originalContent: mention.mentionContent || mention.content,
          responseType: "mention",
          authorInfo: {
            handle: mention.mentionAuthorHandle || mention.authorHandle,
            displayName: mention.mentionAuthor || mention.authorName,
            isVerified: mention.authorIsVerified,
            followerCount: mention.authorFollowerCount,
          },
          userContext: {
            expertise: ["social media", "content creation"],
            interests: ["engagement", "trending topics"],
            writingStyle: "professional",
            brand: "personal brand",
          },
          currentResponses: currentResponseData,
          researchFocus: ["current_events", "topic_background", "recent_developments", "factual_context"],
          maxSteps: 5,
        });

        if (!contextResult.success) {
          throw new Error(contextResult.error || "Failed to research topic");
        }
        
        contextToUse = contextResult.improvedContext;
        setImprovedContext(contextToUse);
        toast.success(`📊 Research complete! Enhancing response with ${contextResult.summary.researchSourcesUsed} sources...`);
      }

      // Generate enhanced response with the research context
      const result = await generateWithImprovedContext({
        originalContent: mention.mentionContent || mention.content,
        improvedContext: contextToUse,
        responseType: "mention",
        style: style,
        userContext: {
          expertise: ["social media", "content creation"],
          interests: ["engagement", "trending topics"],
          writingStyle: style,
          brand: "personal brand",
        },
      });

      if (result.success) {
        // Replace the existing response with enhanced content
        await editResponse({
          responseId: responseId,
          content: result.enhancedResponse.content,
          notes: "Enhanced with AI research insights and current context",
        });
        
        toast.success(`✨ Response enhanced with topic research! Replaced original with context-aware version.`);
      } else {
        throw new Error(result.error || "Enhanced response generation failed");
      }
    } catch (error) {
      console.error("Response enhancement failed:", error);
      toast.error("Failed to enhance response with research context");
    } finally {
      setIsEnhancing(false);
    }
  };

  const getEngagementTotal = () => {
    return (mention.engagement?.likes || 0) + (mention.engagement?.retweets || 0) + (mention.engagement?.replies || 0);
  };

  return (
    <Card 
      className={cn(
        "transition-all duration-300 bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)] cursor-pointer hover:border-[var(--buddychip-accent)]/50 hover:shadow-lg hover:shadow-[var(--buddychip-accent)]/10",
        !mention.isNotificationSent && 'ring-2 ring-[var(--buddychip-accent)] bg-[var(--buddychip-accent)]/10',
        hasEnhancedResponses && 'border-l-4 border-l-[var(--buddychip-accent)] shadow-lg shadow-[var(--buddychip-accent)]/20',
        isHighlighted && 'ring-2 ring-blue-500/50 bg-blue-500/5',
        isExpanded && 'border-[var(--buddychip-accent)]/50 shadow-xl shadow-[var(--buddychip-accent)]/20 bg-[var(--buddychip-accent)]/5'
      )}
      onClick={handleToggleExpanded}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 bg-[var(--buddychip-grey-stroke)] rounded-full flex items-center justify-center">
              <span className="text-sm font-semibold text-[var(--buddychip-white)]">
                {mention.mentionAuthor?.charAt(0).toUpperCase() || mention.authorName?.charAt(0).toUpperCase() || '?'}
              </span>
            </div>
            <div>
              <div className="flex items-center gap-2">
                <span className="font-semibold text-[var(--buddychip-white)]">
                  {mention.mentionAuthor || mention.authorName || 'Unknown'}
                </span>
                <span className="text-[var(--buddychip-grey-text)] text-sm">
                  @{mention.mentionAuthorHandle || mention.authorHandle || 'unknown'}
                </span>
                <div className={`w-2 h-2 rounded-full ${getPriorityColor(mention.priority)}`} />
                <Badge variant="outline" className="text-xs border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-grey-text)]">
                  {mention.mentionType || 'mention'}
                </Badge>
                {mention.priority === 'high' && (
                  <Badge variant="destructive" className="text-xs">
                    High Priority
                  </Badge>
                )}
                {hasEnhancedResponses && (
                  <Badge className="bg-gradient-to-r from-[var(--buddychip-accent)] to-purple-500 text-white text-xs">
                    <Sparkles className="h-3 w-3 mr-1" />
                    Enhanced
                  </Badge>
                )}
                {/* Sentiment Indicator */}
                {mention.sentimentAnalysis && (
                  <SentimentIndicator
                    sentiment={mention.sentimentAnalysis.sentiment}
                    score={mention.sentimentAnalysis.sentimentScore}
                    size="sm"
                    showScore={true}
                  />
                )}
              </div>
              <div className="flex items-center gap-2 text-xs text-[var(--buddychip-grey-text)] mt-1">
                <Calendar className="h-3 w-3 text-white" />
                <span>{formatTimeAgo(mention.createdAt)}</span>
                {mentionResponses && mentionResponses.length > 0 && (
                  <>
                    <span>•</span>
                    <span className="text-[var(--buddychip-accent)]">
                      {mentionResponses.length} response{mentionResponses.length !== 1 ? 's' : ''}
                    </span>
                  </>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2" onClick={(e) => e.stopPropagation()}>
            {!mention.isNotificationSent && (
              <Button 
                size="sm" 
                variant="ghost" 
                className="border border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)]" 
                onClick={handleMarkAsRead}
              >
                <Check className="h-4 w-4 mr-1 text-white" />
                Mark Read
              </Button>
            )}
            {improvedContext && (
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setShowContextInsights(!showContextInsights)}
                className="border border-[var(--buddychip-accent)] text-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)] hover:text-white"
              >
                <Brain className="h-4 w-4 mr-1 text-white" />
                {showContextInsights ? 'Hide' : 'Show'} Insights
              </Button>
            )}
            <Button
              size="sm"
              variant="ghost"
              onClick={handleImproveContext}
              disabled={isImprovingContext}
              className="border border-green-600 text-green-400 hover:bg-green-600 hover:text-white"
            >
              {isImprovingContext ? (
                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
              ) : (
                <Search className="h-4 w-4 mr-1 text-white" />
              )}
              {isImprovingContext ? 'Researching Topic...' : 'Research Topic'}
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]"
              onClick={(e) => {
                e.stopPropagation();
                const url = mention.url || `https://twitter.com/i/web/status/${mention.mentionTweetId}`;
                window.open(url, '_blank');
              }}
            >
              <ExternalLink className="h-4 w-4 text-white" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-white)]"
              onClick={(e) => {
                e.stopPropagation();
                handleToggleExpanded();
              }}
            >
              {isExpanded ? <ChevronUp className="h-4 w-4 text-white" /> : <ChevronDown className="h-4 w-4 text-white" />}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Mention Content */}
        <div className="bg-[var(--buddychip-black)] border border-[var(--buddychip-grey-stroke)] rounded-lg p-4">
          <p className="text-sm leading-relaxed text-[var(--buddychip-white)]">
            {mention.mentionContent || mention.content}
          </p>
          
          {/* Sentiment Analysis Bar */}
          {mention.sentimentAnalysis && (
            <div className="mt-3 pt-3 border-t border-[var(--buddychip-grey-stroke)]">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-[var(--buddychip-grey-text)]">Sentiment Analysis</span>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-[var(--buddychip-grey-text)]">
                      {Math.round(mention.sentimentAnalysis.confidence * 100)}% confidence
                    </span>
                    {mention.sentimentAnalysis.keyWords && mention.sentimentAnalysis.keyWords.length > 0 && (
                      <div className="flex gap-1">
                        {mention.sentimentAnalysis.keyWords.slice(0, 3).map((word: string, idx: number) => (
                          <span key={idx} className="text-xs bg-[var(--buddychip-grey-stroke)] text-[var(--buddychip-grey-text)] px-2 py-1 rounded">
                            {word}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                <SentimentBar
                  sentiment={mention.sentimentAnalysis.sentiment}
                  score={mention.sentimentAnalysis.sentimentScore}
                  confidence={mention.sentimentAnalysis.confidence}
                  compact={true}
                  showLabel={false}
                  showScore={true}
                />
                {mention.sentimentAnalysis.reasoning && (
                  <p className="text-xs text-[var(--buddychip-grey-text)] italic">
                    {mention.sentimentAnalysis.reasoning}
                  </p>
                )}
              </div>
            </div>
          )}
        </div>


        {/* Quick Response Summary (when collapsed) */}
        {!isExpanded && mentionResponses && mentionResponses.length > 0 && (
          <div className="bg-[var(--buddychip-black)]/50 border border-[var(--buddychip-grey-stroke)] rounded-lg p-3 hover:bg-[var(--buddychip-accent)]/5 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Sparkles className="h-4 w-4 text-white" />
                <span className="text-sm font-medium text-[var(--buddychip-white)]">
                  {mentionResponses.length} AI Response{mentionResponses.length !== 1 ? 's' : ''} Ready
                </span>
                {hasEnhancedResponses && (
                  <Badge className="bg-gradient-to-r from-[var(--buddychip-accent)] to-purple-500 text-white text-xs animate-pulse">
                    ✨ {enhancedResponses.length} Enhanced
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-2 text-xs text-[var(--buddychip-grey-text)]">
                <ChevronDown className="h-4 w-4 text-white" />
                <span>Click to expand</span>
              </div>
            </div>
          </div>
        )}

        {/* Expandable hint when no responses */}
        {!isExpanded && (!mentionResponses || mentionResponses.length === 0) && (
          <div className="flex items-center justify-center py-2 text-xs text-[var(--buddychip-grey-text)] hover:text-[var(--buddychip-accent)] transition-colors">
            <ChevronDown className="h-4 w-4 mr-1 text-white" />
            <span>Click to expand and generate AI responses</span>
          </div>
        )}

        {/* Expanded Content */}
        {isExpanded && (
          <div className="space-y-6" onClick={(e) => e.stopPropagation()}>
            <Separator />
            
            {/* Context Insights Panel */}
            {improvedContext && (
              <ContextInsightsPanel
                improvedContext={improvedContext}
                isVisible={showContextInsights}
                onToggleVisibility={() => setShowContextInsights(!showContextInsights)}
              />
            )}

            {/* Enhanced Response Display */}
            {mentionResponses && mentionResponses.length > 0 ? (
              <div>
                <div className="flex items-center justify-between mb-4">
                </div>
                
                <EnhancedResponseDisplay
                  responses={mentionResponses.map(r => ({
                    ...r,
                    isEnhanced: r.model?.includes("Enhanced") || r.isEnhanced,
                    enhancementScore: r.isEnhanced ? 0.25 : undefined,
                  }))}
                  originalUrl={mention.url || `https://twitter.com/i/web/status/${mention.mentionTweetId}`}
                  onCopyResponse={handleCopyResponse}
                  onEditResponse={handleEditResponse}
                  onApproveResponse={handleApproveResponse}
                  onDeclineResponse={handleDeclineResponse}
                  onDeleteResponse={handleDeleteResponse}
                  onEnhanceResponse={handleEnhanceResponse}
                  isEnhancing={isEnhancing}
                />
              </div>
            ) : (
              <div className="space-y-6">
                <div className="text-center py-4">
                  <div className="text-[var(--buddychip-grey-text)] mb-4">
                    No AI responses generated yet for this mention.
                  </div>
                </div>
                
                {/* Integrated Response Generator */}
                <ResponseGenerator
                  targetType="mention"
                  targetId={mention._id}
                  originalContent={mention.mentionContent || mention.content}
                  originalAuthor={mention.mentionAuthorHandle || mention.authorHandle || 'unknown'}
                  originalUrl={mention.url || `https://twitter.com/i/web/status/${mention.mentionTweetId}`}
                  userId={currentUser?._id || ''}
                  onResponseGenerated={() => {
                    // Trigger refresh of the parent component
                    onResponseGenerated?.();
                    // Also show a success message
                    toast.success("AI responses generated! They will appear above.");
                  }}
                />
              </div>
            )}

          </div>
        )}
      </CardContent>
    </Card>
  );
}