import { useState } from "react";
import { Card, CardContent } from "../ui/card";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Filter, X, Search } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { cn } from "../../lib/utils";

interface CollapsibleFiltersProps {
  // Search
  searchQuery: string;
  setSearchQuery: (value: string) => void;
  
  // Priority
  selectedPriority: string;
  setSelectedPriority: (value: string) => void;
  
  // Account
  selectedAccount: string;
  setSelectedAccount: (value: string) => void;
  
  // Time Range
  timeRange: string;
  setTimeRange: (value: string) => void;
  
  // Sentiment
  selectedSentiment: string;
  setSelectedSentiment: (value: string) => void;
  
  // Sort
  sortBy: string;
  setSortBy: (value: string) => void;
  
  // Data
  twitterAccounts?: any[];
  className?: string;
}

export function CollapsibleFilters({
  searchQuery,
  setSearchQuery,
  selectedPriority,
  setSelectedPriority,
  selectedAccount,
  setSelectedAccount,
  timeRange,
  setTimeRange,
  selectedSentiment,
  setSelectedSentiment,
  sortBy,
  setSortBy,
  twitterAccounts,
  className
}: CollapsibleFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleClearFilters = () => {
    setSearchQuery("");
    setSelectedPriority("all");
    setSelectedAccount("all");
    setTimeRange("7d");
    setSelectedSentiment("all");
    setSortBy("newest");
  };

  const hasActiveFilters = 
    searchQuery !== "" || 
    selectedPriority !== "all" || 
    selectedAccount !== "all" || 
    timeRange !== "7d" || 
    selectedSentiment !== "all" || 
    sortBy !== "newest";

  return (
    <div className={cn("relative", className)}>
      {/* Filter Toggle Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsExpanded(!isExpanded)}
        className={cn(
          "border border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)] hover:text-[var(--buddychip-white)] transition-all duration-200",
          hasActiveFilters && "border-[var(--buddychip-accent)] text-[var(--buddychip-accent)]",
          isExpanded && "bg-[var(--buddychip-grey-stroke)]"
        )}
      >
        <Filter className="h-4 w-4 mr-2" />
        Filters
        {hasActiveFilters && (
          <span className="ml-1 bg-[var(--buddychip-accent)] text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {[searchQuery !== "", selectedPriority !== "all", selectedAccount !== "all", timeRange !== "7d", selectedSentiment !== "all", sortBy !== "newest"].filter(Boolean).length}
          </span>
        )}
      </Button>

      {/* Collapsible Filters Panel */}
      {isExpanded && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsExpanded(false)}
          />
          
          {/* Filters Panel */}
          <div className="absolute right-0 top-full mt-2 w-[90vw] max-w-4xl bg-[#000000]/95 border border-[#202631] rounded-lg shadow-xl backdrop-blur-sm z-50 animate-in slide-in-from-top-2 duration-200">
            <Card className="bg-transparent border-0 shadow-none">
              <CardContent className="p-6 space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-[#F5F7FA]">
                    Filters & Search
                  </h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsExpanded(false)}
                    className="text-[#6E7A8C] hover:text-[#F5F7FA] hover:bg-[#202631] p-1"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                {/* Search Bar */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#6E7A8C]" />
                  <Input
                    placeholder="Search mentions, authors, or content..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 bg-[#000000]/40 border-[#202631] text-[#F5F7FA] placeholder:text-[#6E7A8C] focus:border-[#316FE3] focus:ring-[#316FE3]"
                  />
                </div>

                {/* Filter Controls Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
                  {/* Priority Filter */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-[#F5F7FA]">Priority</label>
                    <Select value={selectedPriority} onValueChange={setSelectedPriority}>
                      <SelectTrigger className="bg-[#000000]/40 border-[#202631] text-[#F5F7FA] focus:border-[#316FE3] focus:ring-[#316FE3]">
                        <SelectValue placeholder="All Priorities" />
                      </SelectTrigger>
                      <SelectContent className="bg-[#000000] border-[#202631]">
                        <SelectItem value="all" className="text-[#F5F7FA] focus:bg-[#202631] focus:text-[#F5F7FA]">All Priorities</SelectItem>
                        <SelectItem value="high" className="text-[#F5F7FA] focus:bg-[#202631] focus:text-[#F5F7FA]">High Priority</SelectItem>
                        <SelectItem value="medium" className="text-[#F5F7FA] focus:bg-[#202631] focus:text-[#F5F7FA]">Medium Priority</SelectItem>
                        <SelectItem value="low" className="text-[#F5F7FA] focus:bg-[#202631] focus:text-[#F5F7FA]">Low Priority</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Account Filter */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-[#F5F7FA]">Account</label>
                    <Select value={selectedAccount} onValueChange={setSelectedAccount}>
                      <SelectTrigger className="bg-[#000000]/40 border-[#202631] text-[#F5F7FA] focus:border-[#316FE3] focus:ring-[#316FE3]">
                        <SelectValue placeholder="All Accounts" />
                      </SelectTrigger>
                      <SelectContent className="bg-[#000000] border-[#202631]">
                        <SelectItem value="all" className="text-[#F5F7FA] focus:bg-[#202631] focus:text-[#F5F7FA]">All Accounts</SelectItem>
                        {twitterAccounts?.map((account) => (
                          <SelectItem 
                            key={account._id} 
                            value={account._id}
                            className="text-[#F5F7FA] focus:bg-[#202631] focus:text-[#F5F7FA]"
                          >
                            @{account.handle} {account.monitoringEnabled ? "(Monitoring)" : "(Paused)"}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Time Range Filter */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-[#F5F7FA]">Time Range</label>
                    <Select value={timeRange} onValueChange={setTimeRange}>
                      <SelectTrigger className="bg-[#000000]/40 border-[#202631] text-[#F5F7FA] focus:border-[#316FE3] focus:ring-[#316FE3]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-[#000000] border-[#202631]">
                        <SelectItem value="24h" className="text-[#F5F7FA] focus:bg-[#202631] focus:text-[#F5F7FA]">📅 Last 24 Hours</SelectItem>
                        <SelectItem value="7d" className="text-[#F5F7FA] focus:bg-[#202631] focus:text-[#F5F7FA]">📅 7 Days</SelectItem>
                        <SelectItem value="30d" className="text-[#F5F7FA] focus:bg-[#202631] focus:text-[#F5F7FA]">📅 30 Days</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Sentiment Filter */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-[#F5F7FA]">Sentiment</label>
                    <Select value={selectedSentiment} onValueChange={setSelectedSentiment}>
                      <SelectTrigger className="bg-[#000000]/40 border-[#202631] text-[#F5F7FA] focus:border-[#316FE3] focus:ring-[#316FE3]">
                        <SelectValue placeholder="All Sentiment" />
                      </SelectTrigger>
                      <SelectContent className="bg-[#000000] border-[#202631]">
                        <SelectItem value="all" className="text-[#F5F7FA] focus:bg-[#202631] focus:text-[#F5F7FA]">All Sentiment</SelectItem>
                        <SelectItem value="bullish" className="text-[#F5F7FA] focus:bg-[#202631] focus:text-[#F5F7FA]">📈 Bullish</SelectItem>
                        <SelectItem value="bearish" className="text-[#F5F7FA] focus:bg-[#202631] focus:text-[#F5F7FA]">📉 Bearish</SelectItem>
                        <SelectItem value="neutral" className="text-[#F5F7FA] focus:bg-[#202631] focus:text-[#F5F7FA]">😐 Neutral</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Sort By Filter */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-[#F5F7FA]">Sort By</label>
                    <Select value={sortBy} onValueChange={setSortBy}>
                      <SelectTrigger className="bg-[#000000]/40 border-[#202631] text-[#F5F7FA] focus:border-[#316FE3] focus:ring-[#316FE3]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-[#000000] border-[#202631]">
                        <SelectItem value="newest" className="text-[#F5F7FA] focus:bg-[#202631] focus:text-[#F5F7FA]">🕐 Newest First</SelectItem>
                        <SelectItem value="oldest" className="text-[#F5F7FA] focus:bg-[#202631] focus:text-[#F5F7FA]">🕐 Oldest First</SelectItem>
                        <SelectItem value="priority" className="text-[#F5F7FA] focus:bg-[#202631] focus:text-[#F5F7FA]">⚡ Priority</SelectItem>
                        <SelectItem value="engagement" className="text-[#F5F7FA] focus:bg-[#202631] focus:text-[#F5F7FA]">💬 Engagement</SelectItem>
                        <SelectItem value="sentiment" className="text-[#F5F7FA] focus:bg-[#202631] focus:text-[#F5F7FA]">📊 Sentiment</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t border-[#202631]">
                  <div className="text-sm text-[#6E7A8C]">
                    {hasActiveFilters ? `${[searchQuery !== "", selectedPriority !== "all", selectedAccount !== "all", timeRange !== "7d", selectedSentiment !== "all", sortBy !== "newest"].filter(Boolean).length} filter${[searchQuery !== "", selectedPriority !== "all", selectedAccount !== "all", timeRange !== "7d", selectedSentiment !== "all", sortBy !== "newest"].filter(Boolean).length !== 1 ? 's' : ''} applied` : 'No filters applied'}
                  </div>
                  <div className="flex gap-2">
                    {hasActiveFilters && (
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={handleClearFilters}
                        className="text-[#6E7A8C] hover:text-[#F5F7FA] hover:bg-[#202631]"
                      >
                        Clear Filters
                      </Button>
                    )}
                    <Button
                      size="sm"
                      onClick={() => setIsExpanded(false)}
                      className="bg-[#316FE3] hover:bg-[#2563eb] text-white"
                    >
                      Apply Filters
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </>
      )}
    </div>
  );
}