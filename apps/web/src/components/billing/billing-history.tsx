import React from 'react';
import { useQuery } from 'convex/react';
import { api } from '@BuddyChipAI/backend';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Download, 
  Receipt, 
  Calendar,
  DollarSign,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle
} from 'lucide-react';

interface BillingHistoryItem {
  id: string;
  date: string;
  amount: number;
  currency: string;
  status: 'paid' | 'pending' | 'failed' | 'refunded';
  description: string;
  invoiceUrl?: string;
  planName: string;
}

/**
 * Billing history component showing payment history and invoices
 */
export function BillingHistory() {
  console.log("🧾 Rendering BillingHistory");
  
  const subscription = useQuery(api.billing.subscriptions.getUserSubscription);
  
  // Mock billing history data - in production this would come from <PERSON><PERSON>/Clerk
  const billingHistory: BillingHistoryItem[] = [
    {
      id: "inv_001",
      date: "2024-01-15",
      amount: 49.00,
      currency: "USD",
      status: "paid",
      description: "Pro Plan - Monthly Subscription",
      planName: "Pro",
      invoiceUrl: "#"
    },
    {
      id: "inv_002", 
      date: "2023-12-15",
      amount: 49.00,
      currency: "USD",
      status: "paid",
      description: "Pro Plan - Monthly Subscription",
      planName: "Pro",
      invoiceUrl: "#"
    },
    {
      id: "inv_003",
      date: "2023-11-15", 
      amount: 19.00,
      currency: "USD",
      status: "paid",
      description: "Starter Plan - Monthly Subscription",
      planName: "Starter",
      invoiceUrl: "#"
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'refunded':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      paid: "bg-green-600",
      pending: "bg-yellow-600", 
      failed: "bg-red-600",
      refunded: "bg-orange-600"
    };

    return (
      <Badge className={variants[status as keyof typeof variants] || "bg-gray-600"}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-[var(--buddychip-white)]">
            Billing History
          </h2>
          <p className="text-[var(--buddychip-grey-text)] mt-1">
            View your payment history and download invoices
          </p>
        </div>
        <Button 
          variant="outline"
          className="border-[var(--buddychip-accent)] text-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)] hover:text-white"
        >
          <Download className="h-4 w-4 mr-2" />
          Export All
        </Button>
      </div>

      {/* Billing Summary */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[var(--buddychip-white)]">
              Total Paid
            </CardTitle>
            <DollarSign className="h-4 w-4 text-[var(--buddychip-accent)]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[var(--buddychip-white)]">
              {formatCurrency(
                billingHistory
                  .filter(item => item.status === 'paid')
                  .reduce((sum, item) => sum + item.amount, 0),
                'USD'
              )}
            </div>
            <p className="text-xs text-[var(--buddychip-grey-text)]">
              Lifetime total
            </p>
          </CardContent>
        </Card>

        <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[var(--buddychip-white)]">
              Invoices
            </CardTitle>
            <Receipt className="h-4 w-4 text-[var(--buddychip-accent)]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[var(--buddychip-white)]">
              {billingHistory.length}
            </div>
            <p className="text-xs text-[var(--buddychip-grey-text)]">
              Total invoices
            </p>
          </CardContent>
        </Card>

        <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[var(--buddychip-white)]">
              Next Payment
            </CardTitle>
            <Calendar className="h-4 w-4 text-[var(--buddychip-accent)]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[var(--buddychip-white)]">
              {subscription?.currentPeriodEnd 
                ? new Date(subscription.currentPeriodEnd).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
                : 'N/A'
              }
            </div>
            <p className="text-xs text-[var(--buddychip-grey-text)]">
              Estimated date
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Billing History Table */}
      <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
        <CardHeader>
          <CardTitle className="text-[var(--buddychip-white)]">Payment History</CardTitle>
          <CardDescription className="text-[var(--buddychip-grey-text)]">
            Your complete billing and payment history
          </CardDescription>
        </CardHeader>
        <CardContent>
          {billingHistory.length === 0 ? (
            <div className="text-center py-8">
              <Receipt className="h-12 w-12 text-[var(--buddychip-grey-text)] mx-auto mb-4" />
              <h3 className="text-lg font-medium text-[var(--buddychip-white)] mb-2">
                No billing history
              </h3>
              <p className="text-[var(--buddychip-grey-text)]">
                Your payment history will appear here once you have an active subscription.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {billingHistory.map((item) => (
                <div 
                  key={item.id}
                  className="flex items-center justify-between p-4 border border-[var(--buddychip-grey-stroke)] rounded-lg hover:bg-[var(--buddychip-dark-bg)]/50 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    {getStatusIcon(item.status)}
                    <div>
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium text-[var(--buddychip-white)]">
                          {item.description}
                        </h4>
                        {getStatusBadge(item.status)}
                      </div>
                      <p className="text-sm text-[var(--buddychip-grey-text)]">
                        {formatDate(item.date)} • Invoice #{item.id}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <div className="font-medium text-[var(--buddychip-white)]">
                        {formatCurrency(item.amount, item.currency)}
                      </div>
                      <div className="text-sm text-[var(--buddychip-grey-text)]">
                        {item.planName} Plan
                      </div>
                    </div>
                    
                    {item.invoiceUrl && item.status === 'paid' && (
                      <Button 
                        variant="ghost" 
                        size="sm"
                        className="text-[var(--buddychip-accent)] hover:text-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)]/10"
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payment Method */}
      <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
        <CardHeader>
          <CardTitle className="text-[var(--buddychip-white)]">Payment Method</CardTitle>
          <CardDescription className="text-[var(--buddychip-grey-text)]">
            Manage your default payment method
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded flex items-center justify-center">
                <span className="text-white text-xs font-bold">••••</span>
              </div>
              <div>
                <p className="font-medium text-[var(--buddychip-white)]">
                  •••• •••• •••• 4242
                </p>
                <p className="text-sm text-[var(--buddychip-grey-text)]">
                  Expires 12/25
                </p>
              </div>
            </div>
            <Button 
              variant="outline"
              className="border-[var(--buddychip-grey-stroke)] text-[var(--buddychip-white)] hover:bg-[var(--buddychip-grey-stroke)]"
            >
              Update
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
