import React from 'react';
import { useQuery } from 'convex/react';
import { api } from '@BuddyChipAI/backend';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  CreditCard, 
  Calendar, 
  TrendingUp, 
  AlertCircle, 
  CheckCircle,
  Zap,
  Crown,
  Star,
} from 'lucide-react';
import { useSubscription } from '@/hooks/use-subscription';

/**
 * Comprehensive subscription management dashboard
 * Shows current plan, usage, billing history, and upgrade options
 */
export function SubscriptionDashboard() {
  console.log("🏠 Rendering SubscriptionDashboard");
  
  const subscription = useQuery(api.billing.subscriptions.getUserSubscription);
  const userPermissions = useQuery(api.billing.accessControl.getUserPermissions);
  const userUsage = useQuery(api.billing.usage.getUserUsage);
  const { 
    isActive, 
    daysUntilRenewal
  } = useSubscription();

  // Helper to calculate usage percentage
  const getUsagePercentage = (key: keyof typeof userUsage.usage) => {
    const used = userUsage?.usage[key] || 0;
    const limit = userUsage?.limits[key];
    if (limit === -1 || !limit) return 0;
    return Math.min(100, Math.round((used / limit) * 100));
  };

  // Determine current plan safely
  const currentPlan = (subscription?.planId as 'starter' | 'pro' | 'enterprise') || 'starter';

  const planIcon = {
    starter: Star,
    pro: Zap,
    enterprise: Crown,
  }[currentPlan] || Star;

  const PlanIcon = planIcon;

  // Loading state
  if (subscription === undefined || userPermissions === undefined || userUsage === undefined) {
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
              <CardHeader className="animate-pulse">
                <div className="h-4 bg-[var(--buddychip-grey-stroke)] rounded w-3/4"></div>
                <div className="h-3 bg-[var(--buddychip-grey-stroke)] rounded w-1/2"></div>
              </CardHeader>
              <CardContent className="animate-pulse">
                <div className="h-8 bg-[var(--buddychip-grey-stroke)] rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-[var(--buddychip-white)]">
            Subscription Dashboard
          </h1>
          <p className="text-[var(--buddychip-grey-text)] mt-2">
            Manage your subscription, monitor usage, and upgrade your plan
          </p>
        </div>
        <Button 
          variant="outline" 
          className="border-[var(--buddychip-accent)] text-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)] hover:text-white"
        >
          <CreditCard className="h-4 w-4 mr-2" />
          Billing Settings
        </Button>
      </div>

      {/* Current Plan Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Plan Status */}
        <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[var(--buddychip-white)]">
              Current Plan
            </CardTitle>
            <PlanIcon className="h-4 w-4 text-[var(--buddychip-accent)]" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <div className="text-2xl font-bold text-[var(--buddychip-white)] capitalize">
                {currentPlan}
              </div>
              <Badge 
                variant={isActive ? "default" : "destructive"}
                className={isActive ? "bg-green-600" : "bg-red-600"}
              >
                {isActive ? "Active" : "Inactive"}
              </Badge>
            </div>
            <p className="text-xs text-[var(--buddychip-grey-text)] mt-1">
              {subscription?.status === 'active' && daysUntilRenewal !== null
                ? `Renews in ${daysUntilRenewal} days`
                : subscription?.status === 'canceled'
                ? 'Canceled - Access until period end'
                : 'Status: ' + (subscription?.status || 'Unknown')
              }
            </p>
          </CardContent>
        </Card>

        {/* Billing Period */}
        <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[var(--buddychip-white)]">
              Billing Period
            </CardTitle>
            <Calendar className="h-4 w-4 text-[var(--buddychip-accent)]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[var(--buddychip-white)]">
              {subscription?.currentPeriodEnd 
                ? new Date(subscription.currentPeriodEnd).toLocaleDateString()
                : 'N/A'
              }
            </div>
            <p className="text-xs text-[var(--buddychip-grey-text)]">
              Next billing date
            </p>
          </CardContent>
        </Card>

        {/* Monthly Usage */}
        <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-[var(--buddychip-white)]">
              AI Responses
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-[var(--buddychip-accent)]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[var(--buddychip-white)]">
              {userUsage?.usage.aiResponses || 0}
            </div>
            <p className="text-xs text-[var(--buddychip-grey-text)]">
              of {userUsage?.limits.aiResponses === -1 ? '∞' : userUsage?.limits.aiResponses || 0} this month
            </p>
            {userUsage?.limits.aiResponses !== -1 && (
              <Progress 
                value={getUsagePercentage('aiResponses')} 
                className="mt-2"
              />
            )}
          </CardContent>
        </Card>
      </div>

      {/* Usage Details */}
      <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
        <CardHeader>
          <CardTitle className="text-[var(--buddychip-white)]">Usage Details</CardTitle>
          <CardDescription className="text-[var(--buddychip-grey-text)]">
            Monitor your feature usage and limits
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {/* AI Responses */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-[var(--buddychip-white)]">AI Responses</span>
                <span className="text-sm text-[var(--buddychip-grey-text)]">
                  {userUsage?.usage.aiResponses || 0} / {userUsage?.limits.aiResponses === -1 ? '∞' : userUsage?.limits.aiResponses || 0}
                </span>
              </div>
              {userUsage?.limits.aiResponses !== -1 && (
                <Progress value={getUsagePercentage('aiResponses')} />
              )}
            </div>

            {/* Image Generations */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-[var(--buddychip-white)]">Image Generations</span>
                <span className="text-sm text-[var(--buddychip-grey-text)]">
                  {userUsage?.usage.imageGenerations || 0} / {userUsage?.limits.imageGenerations === -1 ? '∞' : userUsage?.limits.imageGenerations || 0}
                </span>
              </div>
              {userUsage?.limits.imageGenerations !== -1 && (
                <Progress value={getUsagePercentage('imageGenerations')} />
              )}
            </div>

            {/* API Requests */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-[var(--buddychip-white)]">API Requests</span>
                <span className="text-sm text-[var(--buddychip-grey-text)]">
                  {userUsage?.usage.apiRequests || 0} / {userUsage?.limits.apiRequests === -1 ? '∞' : userUsage?.limits.apiRequests || 0}
                </span>
              </div>
              {userUsage?.limits.apiRequests !== -1 && (
                <Progress value={getUsagePercentage('apiRequests')} />
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Feature Access */}
      <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-grey-stroke)]">
        <CardHeader>
          <CardTitle className="text-[var(--buddychip-white)]">Feature Access</CardTitle>
          <CardDescription className="text-[var(--buddychip-grey-text)]">
            Features available with your current plan
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
            {Object.entries(userPermissions?.features || {}).map(([feature, hasAccess]) => (
              <div key={feature} className="flex items-center space-x-2">
                {hasAccess ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-500" />
                )}
                <span className={`text-sm ${hasAccess ? 'text-[var(--buddychip-white)]' : 'text-[var(--buddychip-grey-text)]'}`}>
                  {feature.replace(/([A-Z])/g, ' $1').replace(/^./, (str: string) => str.toUpperCase())}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Upgrade Prompt */}
      {currentPlan !== 'enterprise' && (
        <Card className="bg-gradient-to-r from-[var(--buddychip-accent)]/10 to-[var(--buddychip-secondary)]/10 border-[var(--buddychip-accent)]">
          <CardHeader>
            <CardTitle className="text-[var(--buddychip-white)]">
              Unlock More Features
            </CardTitle>
            <CardDescription className="text-[var(--buddychip-grey-text)]">
              Upgrade your plan to access premium features and higher limits
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-[var(--buddychip-white)] mb-2">
                  {currentPlan === 'starter' 
                    ? 'Upgrade to Pro for premium AI, image generation, and analytics'
                    : 'Upgrade to Enterprise for unlimited usage and white-label options'
                  }
                </p>
              </div>
              <Button className="bg-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)]/90">
                Upgrade Now
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
