import React from 'react';
import { useQuery } from 'convex/react';
import { api } from '@BuddyChipAI/backend';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Lock, Zap, Crown, ArrowRight } from 'lucide-react';
import { Link } from '@tanstack/react-router';

interface SubscriptionGuardProps {
  children: React.ReactNode;
  feature?: string;
  plan?: 'starter' | 'pro' | 'enterprise';
  fallback?: React.ReactNode;
  showUpgrade?: boolean;
}

/**
 * Guard component that protects content based on subscription features or plans
 */
export function SubscriptionGuard({ 
  children, 
  feature, 
  plan, 
  fallback, 
  showUpgrade = true 
}: SubscriptionGuardProps) {
  console.log(`🔐 SubscriptionGuard: Checking access for feature: ${feature}, plan: ${plan}`);
  
  const featureAccess = useQuery(
    api.billing.accessControl.hasFeatureAccess,
    feature ? { feature: feature as any } : "skip"
  );
  
  const planAccess = useQuery(
    api.billing.accessControl.hasPlanAccess,
    plan ? { planId: plan } : "skip"
  );
  
  const userPermissions = useQuery(api.billing.accessControl.getUserPermissions);

  // Loading state
  if (
    (feature && featureAccess === undefined) || 
    (plan && planAccess === undefined) ||
    userPermissions === undefined
  ) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-[var(--buddychip-grey-text)]">Checking access...</div>
      </div>
    );
  }

  // Check feature access
  if (feature && featureAccess && !featureAccess.hasAccess) {
    console.log(`❌ Feature access denied: ${feature}`);
    if (fallback) return <>{fallback}</>;
    if (!showUpgrade) return null;
    
    return (
      <FeatureUpgradePrompt 
        feature={feature}
        currentPlan={userPermissions?.planId || 'starter'}
        reason={featureAccess.reason}
      />
    );
  }

  // Check plan access
  if (plan && planAccess && !planAccess.hasAccess) {
    console.log(`❌ Plan access denied: ${plan}`);
    if (fallback) return <>{fallback}</>;
    if (!showUpgrade) return null;
    
    return (
      <PlanUpgradePrompt 
        requiredPlan={plan}
        currentPlan={planAccess.userPlanId || 'starter'}
        reason={planAccess.reason}
      />
    );
  }

  console.log(`✅ Access granted`);
  return <>{children}</>;
}

/**
 * Component for feature-specific upgrade prompts
 */
function FeatureUpgradePrompt({ 
  feature, 
  currentPlan, 
  reason 
}: { 
  feature: string; 
  currentPlan: string; 
  reason?: string; 
}) {
  const featureNames: Record<string, string> = {
    premiumAi: 'Premium AI Models',
    imageGeneration: 'AI Image Generation',
    bulkProcessing: 'Bulk Processing',
    advancedAnalytics: 'Advanced Analytics',
    prioritySupport: 'Priority Support',
    customIntegrations: 'Custom Integrations',
    whiteLabel: 'White Label Options',
  };

  const featureName = featureNames[feature] || feature;
  const requiredPlan = getRequiredPlanForFeature(feature);

  return (
    <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-accent)] border-2">
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <Lock className="h-12 w-12 text-[var(--buddychip-accent)]" />
        </div>
        <CardTitle className="text-xl text-[var(--buddychip-white)]">
          {featureName} Locked
        </CardTitle>
        <CardDescription className="text-[var(--buddychip-grey-text)]">
          {reason || `This feature requires a higher subscription plan`}
        </CardDescription>
      </CardHeader>
      <CardContent className="text-center space-y-4">
        <div className="flex justify-center items-center space-x-2">
          <Badge variant="outline" className="text-[var(--buddychip-grey-text)]">
            Current: {currentPlan}
          </Badge>
          <ArrowRight className="h-4 w-4 text-[var(--buddychip-grey-text)]" />
          <Badge className="bg-[var(--buddychip-accent)] text-[var(--buddychip-white)]">
            Required: {requiredPlan}
          </Badge>
        </div>
        
        <div className="space-y-2">
          <Link to="/pricing">
            <Button className="w-full bg-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)]/80">
              <Zap className="h-4 w-4 mr-2" />
              Upgrade to {requiredPlan}
            </Button>
          </Link>
          <Link to="/pricing">
            <Button variant="ghost" className="w-full text-[var(--buddychip-grey-text)]">
              View All Plans
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Component for plan-specific upgrade prompts
 */
function PlanUpgradePrompt({ 
  requiredPlan, 
  currentPlan, 
  reason 
}: { 
  requiredPlan: string; 
  currentPlan: string; 
  reason?: string; 
}) {
  const planIcons = {
    starter: <Lock className="h-12 w-12 text-[var(--buddychip-accent)]" />,
    pro: <Zap className="h-12 w-12 text-[var(--buddychip-accent)]" />,
    enterprise: <Crown className="h-12 w-12 text-[var(--buddychip-secondary)]" />,
  };

  const planColors = {
    starter: 'var(--buddychip-accent)',
    pro: 'var(--buddychip-accent)',
    enterprise: 'var(--buddychip-secondary)',
  };

  return (
    <Card className="bg-[var(--buddychip-light-bg)] border-2" style={{ borderColor: planColors[requiredPlan as keyof typeof planColors] }}>
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          {planIcons[requiredPlan as keyof typeof planIcons]}
        </div>
        <CardTitle className="text-xl text-[var(--buddychip-white)]">
          {requiredPlan.charAt(0).toUpperCase() + requiredPlan.slice(1)} Plan Required
        </CardTitle>
        <CardDescription className="text-[var(--buddychip-grey-text)]">
          {reason || `This feature requires the ${requiredPlan} plan or higher`}
        </CardDescription>
      </CardHeader>
      <CardContent className="text-center space-y-4">
        <div className="flex justify-center items-center space-x-2">
          <Badge variant="outline" className="text-[var(--buddychip-grey-text)]">
            Current: {currentPlan}
          </Badge>
          <ArrowRight className="h-4 w-4 text-[var(--buddychip-grey-text)]" />
          <Badge style={{ backgroundColor: planColors[requiredPlan as keyof typeof planColors] }}>
            Required: {requiredPlan}
          </Badge>
        </div>
        
        <div className="space-y-2">
          <Link to="/pricing">
            <Button 
              className="w-full"
              style={{ 
                backgroundColor: planColors[requiredPlan as keyof typeof planColors],
                color: 'var(--buddychip-white)'
              }}
            >
              <Zap className="h-4 w-4 mr-2" />
              Upgrade to {requiredPlan.charAt(0).toUpperCase() + requiredPlan.slice(1)}
            </Button>
          </Link>
          <Link to="/pricing">
            <Button variant="ghost" className="w-full text-[var(--buddychip-grey-text)]">
              Compare All Plans
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Helper function to determine required plan for a feature
 */
function getRequiredPlanForFeature(feature: string): string {
  const featurePlanMap: Record<string, string> = {
    basicMonitoring: 'starter',
    premiumAi: 'pro',
    imageGeneration: 'pro',
    bulkProcessing: 'enterprise',
    advancedAnalytics: 'pro',
    prioritySupport: 'pro',
    customIntegrations: 'enterprise',
    whiteLabel: 'enterprise',
  };

  return featurePlanMap[feature] || 'pro';
}

/**
 * Usage limit guard component
 */
interface UsageLimitGuardProps {
  children: React.ReactNode;
  feature: 'aiResponses' | 'imageGenerations' | 'apiRequests' | 'bulkOperations' | 'premiumAiCalls' | 'analyticsQueries';
  amount?: number;
  fallback?: React.ReactNode;
  showUpgrade?: boolean;
}

export function UsageLimitGuard({ 
  children, 
  feature, 
  amount = 1, 
  fallback, 
  showUpgrade = true 
}: UsageLimitGuardProps) {
  const canPerform = useQuery(api.billing.usage.canPerformAction, { feature, amount });
  const userPermissions = useQuery(api.billing.accessControl.getUserPermissions);

  if (canPerform === undefined || userPermissions === undefined) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="text-[var(--buddychip-grey-text)]">Checking usage limits...</div>
      </div>
    );
  }

  if (!canPerform.canPerform) {
    if (fallback) return <>{fallback}</>;
    if (!showUpgrade) return null;
    
    return (
      <UsageLimitPrompt 
        feature={feature}
        currentPlan={userPermissions.planId}
        currentUsage={canPerform.currentUsage}
        limit={canPerform.limit}
        reason={canPerform.reason}
      />
    );
  }

  return <>{children}</>;
}

/**
 * Component for usage limit upgrade prompts
 */
function UsageLimitPrompt({ 
  feature, 
  currentPlan, 
  currentUsage, 
  limit, 
  reason 
}: { 
  feature: string; 
  currentPlan: string; 
  currentUsage?: number; 
  limit?: number; 
  reason?: string; 
}) {
  const featureNames: Record<string, string> = {
    aiResponses: 'AI Responses',
    imageGenerations: 'Image Generations',
    apiRequests: 'API Requests',
    bulkOperations: 'Bulk Operations',
    premiumAiCalls: 'Premium AI Calls',
    analyticsQueries: 'Analytics Queries',
  };

  const featureName = featureNames[feature] || feature;

  return (
    <Card className="bg-[var(--buddychip-light-bg)] border-[var(--buddychip-accent)] border-2">
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <Lock className="h-12 w-12 text-[var(--buddychip-accent)]" />
        </div>
        <CardTitle className="text-xl text-[var(--buddychip-white)]">
          {featureName} Limit Reached
        </CardTitle>
        <CardDescription className="text-[var(--buddychip-grey-text)]">
          {reason || `You've reached your daily limit for ${featureName.toLowerCase()}`}
        </CardDescription>
      </CardHeader>
      <CardContent className="text-center space-y-4">
        {currentUsage !== undefined && limit !== undefined && (
          <div className="text-[var(--buddychip-grey-text)]">
            Used: {currentUsage}/{limit === -1 ? '∞' : limit}
          </div>
        )}
        
        <div className="space-y-2">
          <Link to="/pricing">
            <Button className="w-full bg-[var(--buddychip-accent)] hover:bg-[var(--buddychip-accent)]/80">
              <Zap className="h-4 w-4 mr-2" />
              Upgrade for Higher Limits
            </Button>
          </Link>
          <Link to="/pricing">
            <Button variant="ghost" className="w-full text-[var(--buddychip-grey-text)]">
              View All Plans
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
