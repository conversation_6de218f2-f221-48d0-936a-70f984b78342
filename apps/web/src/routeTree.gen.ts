/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as TweetAssistantRouteImport } from './routes/tweet-assistant'
import { Route as SignUpRouteImport } from './routes/sign-up'
import { Route as SignInRouteImport } from './routes/sign-in'
import { Route as PricingRouteImport } from './routes/pricing'
import { Route as OnboardingRouteImport } from './routes/onboarding'
import { Route as MentionsRouteImport } from './routes/mentions'
import { Route as LiveSearchRouteImport } from './routes/live-search'
import { Route as ImageGenerationRouteImport } from './routes/image-generation'
import { Route as DashboardRouteImport } from './routes/dashboard'
import { Route as BillingRouteImport } from './routes/billing'
import { Route as R404RouteImport } from './routes/404'
import { Route as SplatRouteImport } from './routes/$splat'
import { Route as IndexRouteImport } from './routes/index'

const TweetAssistantRoute = TweetAssistantRouteImport.update({
  id: '/tweet-assistant',
  path: '/tweet-assistant',
  getParentRoute: () => rootRouteImport,
} as any)
const SignUpRoute = SignUpRouteImport.update({
  id: '/sign-up',
  path: '/sign-up',
  getParentRoute: () => rootRouteImport,
} as any)
const SignInRoute = SignInRouteImport.update({
  id: '/sign-in',
  path: '/sign-in',
  getParentRoute: () => rootRouteImport,
} as any)
const PricingRoute = PricingRouteImport.update({
  id: '/pricing',
  path: '/pricing',
  getParentRoute: () => rootRouteImport,
} as any)
const OnboardingRoute = OnboardingRouteImport.update({
  id: '/onboarding',
  path: '/onboarding',
  getParentRoute: () => rootRouteImport,
} as any)
const MentionsRoute = MentionsRouteImport.update({
  id: '/mentions',
  path: '/mentions',
  getParentRoute: () => rootRouteImport,
} as any)
const LiveSearchRoute = LiveSearchRouteImport.update({
  id: '/live-search',
  path: '/live-search',
  getParentRoute: () => rootRouteImport,
} as any)
const ImageGenerationRoute = ImageGenerationRouteImport.update({
  id: '/image-generation',
  path: '/image-generation',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardRoute = DashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRouteImport,
} as any)
const BillingRoute = BillingRouteImport.update({
  id: '/billing',
  path: '/billing',
  getParentRoute: () => rootRouteImport,
} as any)
const R404Route = R404RouteImport.update({
  id: '/404',
  path: '/404',
  getParentRoute: () => rootRouteImport,
} as any)
const SplatRoute = SplatRouteImport.update({
  id: '/$splat',
  path: '/$splat',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/$splat': typeof SplatRoute
  '/404': typeof R404Route
  '/billing': typeof BillingRoute
  '/dashboard': typeof DashboardRoute
  '/image-generation': typeof ImageGenerationRoute
  '/live-search': typeof LiveSearchRoute
  '/mentions': typeof MentionsRoute
  '/onboarding': typeof OnboardingRoute
  '/pricing': typeof PricingRoute
  '/sign-in': typeof SignInRoute
  '/sign-up': typeof SignUpRoute
  '/tweet-assistant': typeof TweetAssistantRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/$splat': typeof SplatRoute
  '/404': typeof R404Route
  '/billing': typeof BillingRoute
  '/dashboard': typeof DashboardRoute
  '/image-generation': typeof ImageGenerationRoute
  '/live-search': typeof LiveSearchRoute
  '/mentions': typeof MentionsRoute
  '/onboarding': typeof OnboardingRoute
  '/pricing': typeof PricingRoute
  '/sign-in': typeof SignInRoute
  '/sign-up': typeof SignUpRoute
  '/tweet-assistant': typeof TweetAssistantRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/$splat': typeof SplatRoute
  '/404': typeof R404Route
  '/billing': typeof BillingRoute
  '/dashboard': typeof DashboardRoute
  '/image-generation': typeof ImageGenerationRoute
  '/live-search': typeof LiveSearchRoute
  '/mentions': typeof MentionsRoute
  '/onboarding': typeof OnboardingRoute
  '/pricing': typeof PricingRoute
  '/sign-in': typeof SignInRoute
  '/sign-up': typeof SignUpRoute
  '/tweet-assistant': typeof TweetAssistantRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/$splat'
    | '/404'
    | '/billing'
    | '/dashboard'
    | '/image-generation'
    | '/live-search'
    | '/mentions'
    | '/onboarding'
    | '/pricing'
    | '/sign-in'
    | '/sign-up'
    | '/tweet-assistant'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/$splat'
    | '/404'
    | '/billing'
    | '/dashboard'
    | '/image-generation'
    | '/live-search'
    | '/mentions'
    | '/onboarding'
    | '/pricing'
    | '/sign-in'
    | '/sign-up'
    | '/tweet-assistant'
  id:
    | '__root__'
    | '/'
    | '/$splat'
    | '/404'
    | '/billing'
    | '/dashboard'
    | '/image-generation'
    | '/live-search'
    | '/mentions'
    | '/onboarding'
    | '/pricing'
    | '/sign-in'
    | '/sign-up'
    | '/tweet-assistant'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  SplatRoute: typeof SplatRoute
  R404Route: typeof R404Route
  BillingRoute: typeof BillingRoute
  DashboardRoute: typeof DashboardRoute
  ImageGenerationRoute: typeof ImageGenerationRoute
  LiveSearchRoute: typeof LiveSearchRoute
  MentionsRoute: typeof MentionsRoute
  OnboardingRoute: typeof OnboardingRoute
  PricingRoute: typeof PricingRoute
  SignInRoute: typeof SignInRoute
  SignUpRoute: typeof SignUpRoute
  TweetAssistantRoute: typeof TweetAssistantRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/tweet-assistant': {
      id: '/tweet-assistant'
      path: '/tweet-assistant'
      fullPath: '/tweet-assistant'
      preLoaderRoute: typeof TweetAssistantRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/sign-up': {
      id: '/sign-up'
      path: '/sign-up'
      fullPath: '/sign-up'
      preLoaderRoute: typeof SignUpRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/sign-in': {
      id: '/sign-in'
      path: '/sign-in'
      fullPath: '/sign-in'
      preLoaderRoute: typeof SignInRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/pricing': {
      id: '/pricing'
      path: '/pricing'
      fullPath: '/pricing'
      preLoaderRoute: typeof PricingRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/onboarding': {
      id: '/onboarding'
      path: '/onboarding'
      fullPath: '/onboarding'
      preLoaderRoute: typeof OnboardingRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/mentions': {
      id: '/mentions'
      path: '/mentions'
      fullPath: '/mentions'
      preLoaderRoute: typeof MentionsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/live-search': {
      id: '/live-search'
      path: '/live-search'
      fullPath: '/live-search'
      preLoaderRoute: typeof LiveSearchRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/image-generation': {
      id: '/image-generation'
      path: '/image-generation'
      fullPath: '/image-generation'
      preLoaderRoute: typeof ImageGenerationRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/billing': {
      id: '/billing'
      path: '/billing'
      fullPath: '/billing'
      preLoaderRoute: typeof BillingRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/404': {
      id: '/404'
      path: '/404'
      fullPath: '/404'
      preLoaderRoute: typeof R404RouteImport
      parentRoute: typeof rootRouteImport
    }
    '/$splat': {
      id: '/$splat'
      path: '/$splat'
      fullPath: '/$splat'
      preLoaderRoute: typeof SplatRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  SplatRoute: SplatRoute,
  R404Route: R404Route,
  BillingRoute: BillingRoute,
  DashboardRoute: DashboardRoute,
  ImageGenerationRoute: ImageGenerationRoute,
  LiveSearchRoute: LiveSearchRoute,
  MentionsRoute: MentionsRoute,
  OnboardingRoute: OnboardingRoute,
  PricingRoute: PricingRoute,
  SignInRoute: SignInRoute,
  SignUpRoute: SignUpRoute,
  TweetAssistantRoute: TweetAssistantRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
