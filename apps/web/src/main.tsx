import { RouterProvider, createRouter } from "@tanstack/react-router";
import ReactDOM from "react-dom/client";
import Loader from "./components/loader";
import { routeTree } from "./routeTree.gen";
import { NavigationErrorBoundary } from "./components/error/navigation-error-boundary";
import { NotFoundPage } from "./components/error/not-found-page";

import { ClerkProv<PERSON> } from "@clerk/clerk-react";
import { ConvexReactClient } from "convex/react";
import { SolanaWalletProvider } from "./components/wallet/solana-wallet-provider";
import { ClerkConvexWrapper } from "./components/auth/clerk-convex-wrapper";

const convex = new ConvexReactClient(import.meta.env.VITE_CONVEX_URL as string);

// Initialize 404 system logging
console.log('🚀 BuddyChipPro 404 Error Handling System Initialized');
console.log('📋 Features enabled:');
console.log('  ✅ Custom 404 page with BuddyChip branding');
console.log('  ✅ Intelligent URL redirects and typo correction');
console.log('  ✅ Analytics tracking for 404 errors');
console.log('  ✅ Navigation error boundaries');
console.log('  ✅ User-friendly search and navigation options');

const router = createRouter({
  routeTree,
  defaultPreload: "intent",
  defaultPendingComponent: () => <Loader />,
  // Add error handling for navigation failures
  defaultErrorComponent: ({ error }) => {
    console.error('🔴 Router error:', error);
    return (
      <NotFoundPage
        message="Something went wrong while loading this page."
        showSearch={true}
        showSuggestions={true}
      />
    );
  },
  // Add not found component for unmatched routes
  defaultNotFoundComponent: () => {
    console.log('🔍 Router: No route matched, showing 404');
    return (
      <NotFoundPage
        requestedUrl={typeof window !== 'undefined' ? window.location.pathname : '/unknown'}
        showSearch={true}
        showSuggestions={true}
      />
    );
  },
  context: {},
  Wrap: function WrapComponent({ children }: { children: React.ReactNode }) {
    return (
      <ClerkProvider
        publishableKey={import.meta.env.VITE_CLERK_PUBLISHABLE_KEY}
        afterSignOutUrl="/"
      >
        <ClerkConvexWrapper client={convex}>
          <SolanaWalletProvider>
            <NavigationErrorBoundary>
              {children}
            </NavigationErrorBoundary>
          </SolanaWalletProvider>
        </ClerkConvexWrapper>
      </ClerkProvider>
    );
  },
});

declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}

const rootElement = document.getElementById("app");

if (!rootElement) {
  throw new Error("Root element not found");
}

if (!rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement);
  root.render(<RouterProvider router={router} />);
}
