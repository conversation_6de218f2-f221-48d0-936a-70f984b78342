/**
 * Catch-all route for TanStack Router
 * Simplified version to avoid circular dependencies during build
 */

import { createFileRoute } from "@tanstack/react-router";
import { NotFoundPage } from "@/components/error/not-found-page";

export const Route = createFileRoute("/$splat")({
  component: CatchAllComponent,
});

function CatchAllComponent() {
  const splat = Route.useParams().splat;
  const requestedUrl = `/${splat}`;

  return (
    <NotFoundPage 
      requestedUrl={requestedUrl}
      showSearch={true}
      showSuggestions={false}
    />
  );
}
